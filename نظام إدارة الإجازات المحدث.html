<!DOCTYPE html>
<html dir="rtl" lang="ar">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>نظام إدارة إجازات الموظفين المتطور</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', 'Tahoma', 'Arial', 'Helvetica', sans-serif;
            background: #f5f7fa;
            min-height: 100vh;
            direction: rtl;
            color: #333;
            overflow-x: hidden;
            -webkit-font-smoothing: antialiased;
            -moz-osx-font-smoothing: grayscale;
        }

        /* Sidebar Styles */
        .sidebar {
            position: fixed;
            top: 0;
            right: 0;
            width: 280px;
            height: 100vh;
            background: linear-gradient(180deg, #2c3e50 0%, #34495e 100%);
            color: white;
            z-index: 1000;
            transition: transform 0.3s ease;
            box-shadow: -2px 0 10px rgba(0,0,0,0.1);
        }

        .sidebar.collapsed {
            transform: translateX(220px);
        }

        .sidebar-header {
            padding: 20px;
            background: rgba(0,0,0,0.1);
            border-bottom: 1px solid rgba(255,255,255,0.1);
        }

        .sidebar-title {
            font-size: 18px;
            font-weight: bold;
            margin-bottom: 5px;
        }

        .sidebar-subtitle {
            font-size: 12px;
            opacity: 0.7;
        }

        .sidebar-toggle {
            position: absolute;
            top: 20px;
            left: 15px;
            background: none;
            border: none;
            color: white;
            font-size: 18px;
            cursor: pointer;
            padding: 5px;
            border-radius: 3px;
            transition: background 0.3s;
        }

        .sidebar-toggle:hover {
            background: rgba(255,255,255,0.1);
        }

        .sidebar-nav {
            padding: 20px 0;
        }

        .nav-item {
            display: flex;
            align-items: center;
            padding: 15px 20px;
            cursor: pointer;
            transition: all 0.3s;
            border-right: 3px solid transparent;
        }

        .nav-item:hover {
            background: rgba(255,255,255,0.1);
            border-right-color: #3498db;
        }

        .nav-item.active {
            background: rgba(52, 152, 219, 0.2);
            border-right-color: #3498db;
        }

        .nav-icon {
            font-size: 20px;
            margin-left: 15px;
            width: 25px;
            text-align: center;
        }

        .nav-text {
            font-size: 14px;
            font-weight: 500;
        }

        /* Main Content Area */
        .main-container {
            margin-right: 280px;
            min-height: 100vh;
            transition: margin-right 0.3s ease;
        }

        .main-container.expanded {
            margin-right: 60px;
        }

        .main-header {
            background: white;
            padding: 20px 30px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            border-bottom: 1px solid #e9ecef;
            position: relative;
        }

        .main-title {
            font-size: 24px;
            font-weight: 600;
            color: #2c3e50;
            margin-bottom: 5px;
        }

        .main-subtitle {
            font-size: 14px;
            color: #7f8c8d;
        }

        .save-indicator {
            position: absolute;
            top: 20px;
            left: 30px;
            background: rgba(39, 174, 96, 0.1);
            color: #27ae60;
            padding: 8px 15px;
            border-radius: 20px;
            font-size: 12px;
            border: 1px solid rgba(39, 174, 96, 0.2);
        }

        .save-indicator.saving {
            background: rgba(255,193,7,0.1);
            color: #f39c12;
            border-color: rgba(255,193,7,0.2);
        }

        .main-content {
            padding: 30px;
        }

        /* Section Styles */
        .section {
            display: none;
            animation: fadeIn 0.3s ease;
        }

        .section.active {
            display: block;
        }

        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(20px); }
            to { opacity: 1; transform: translateY(0); }
        }

        /* Card Styles */
        .card {
            background: white;
            border-radius: 12px;
            box-shadow: 0 4px 20px rgba(0,0,0,0.08);
            margin-bottom: 30px;
            overflow: hidden;
        }

        .card-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px 30px;
            border-bottom: 1px solid rgba(255,255,255,0.1);
        }

        .card-title {
            font-size: 18px;
            font-weight: 600;
            margin: 0;
        }

        .card-body {
            padding: 30px;
        }

        /* Form Styles */
        .form-row {
            display: flex;
            gap: 20px;
            margin-bottom: 20px;
            flex-wrap: wrap;
            align-items: end;
        }

        .form-group {
            flex: 1;
            min-width: 200px;
        }

        .form-group label {
            display: block;
            margin-bottom: 8px;
            font-weight: 600;
            color: #2c3e50;
        }

        .form-group input, .form-group select, .form-group textarea {
            width: 100%;
            padding: 12px;
            border: 2px solid #e9ecef;
            border-radius: 8px;
            font-size: 14px;
            transition: border-color 0.3s;
        }

        .form-group input:focus, .form-group select:focus, .form-group textarea:focus {
            outline: none;
            border-color: #3498db;
        }

        /* Button Styles */
        .btn {
            background: linear-gradient(135deg, #3498db, #2980b9);
            color: white;
            border: none;
            padding: 12px 25px;
            border-radius: 8px;
            cursor: pointer;
            font-size: 14px;
            font-weight: 600;
            transition: all 0.3s;
            margin: 5px;
            display: inline-flex;
            align-items: center;
            gap: 8px;
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 15px rgba(52, 152, 219, 0.3);
        }

        .btn-primary {
            background: linear-gradient(135deg, #3498db, #2980b9);
        }

        .btn-success {
            background: linear-gradient(135deg, #27ae60, #229954);
        }

        .btn-warning {
            background: linear-gradient(135deg, #f39c12, #e67e22);
        }

        .btn-danger {
            background: linear-gradient(135deg, #e74c3c, #c0392b);
        }

        .btn-secondary {
            background: linear-gradient(135deg, #95a5a6, #7f8c8d);
        }

        .btn-small {
            padding: 8px 15px;
            font-size: 12px;
        }

        .btn-large {
            padding: 15px 30px;
            font-size: 16px;
        }

        /* Stats Grid */
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }

        .stat-card {
            background: white;
            padding: 25px;
            border-radius: 12px;
            box-shadow: 0 4px 20px rgba(0,0,0,0.08);
            text-align: center;
            border-top: 4px solid #3498db;
        }

        .stat-card h3 {
            margin: 0 0 10px 0;
            color: #2c3e50;
            font-size: 14px;
        }

        .stat-card .number {
            font-size: 2.5em;
            font-weight: bold;
            color: #3498db;
            margin-bottom: 5px;
        }

        .stat-card .change {
            font-size: 12px;
            color: #7f8c8d;
        }

        /* Alert Styles */
        .alert {
            padding: 15px 20px;
            margin: 20px 0;
            border-radius: 8px;
            font-weight: 500;
            border-left: 4px solid;
        }

        .alert-success {
            background: #d4edda;
            color: #155724;
            border-left-color: #28a745;
        }

        .alert-warning {
            background: #fff3cd;
            color: #856404;
            border-left-color: #ffc107;
        }

        .alert-danger {
            background: #f8d7da;
            color: #721c24;
            border-left-color: #dc3545;
        }

        .alert-info {
            background: #d1ecf1;
            color: #0c5460;
            border-left-color: #17a2b8;
        }

        /* Search input with icon */
        .search-input {
            background-image: url('data:image/svg+xml;charset=utf-8,%3Csvg xmlns=\'http://www.w3.org/2000/svg\' width=\'16\' height=\'16\' fill=\'%23999\' viewBox=\'0 0 16 16\'%3E%3Cpath d=\'M11.742 10.344a6.5 6.5 0 1 0-1.397 1.398h-.001c.03.04.062.078.098.115l3.85 3.85a1 1 0 0 0 1.415-1.414l-3.85-3.85a1.007 1.007 0 0 0-.115-.1zM12 6.5a5.5 5.5 0 1 1-11 0 5.5 5.5 0 0 1 11 0z\'/%3E%3C/svg%3E');
            background-repeat: no-repeat;
            background-position: 12px center;
            padding-left: 40px !important;
        }

        /* Fix for table responsiveness */
        .table-responsive {
            overflow-x: auto;
            -webkit-overflow-scrolling: touch;
        }

        .table-responsive table {
            min-width: 1200px;
            white-space: nowrap;
        }

        /* Enhanced button styles */
        .btn-group {
            display: flex;
            gap: 5px;
            flex-wrap: wrap;
        }

        /* Fix for Arabic text rendering */
        * {
            font-family: 'Segoe UI', 'Tahoma', 'Arial', sans-serif;
        }

        /* Clean text rendering */
        input, select, textarea, button {
            font-family: inherit;
            text-rendering: optimizeLegibility;
        }

        /* Hide any malformed content */
        .hidden-content {
            display: none !important;
        }

        /* Improved spacing */
        .form-row {
            margin-bottom: 25px;
        }

        .card + .card {
            margin-top: 30px;
        }

        /* Pagination Styles */
        .pagination-container {
            display: flex;
            justify-content: center;
            align-items: center;
            margin: 20px 0;
            gap: 10px;
        }

        .pagination-btn {
            padding: 8px 12px;
            border: 1px solid #ddd;
            background: white;
            color: #333;
            cursor: pointer;
            border-radius: 4px;
            transition: all 0.3s;
        }

        .pagination-btn:hover {
            background: #f8f9fa;
        }

        .pagination-btn.active {
            background: #3498db;
            color: white;
            border-color: #3498db;
        }

        .pagination-btn:disabled {
            opacity: 0.5;
            cursor: not-allowed;
        }

        .pagination-info {
            font-size: 14px;
            color: #666;
            margin: 0 15px;
        }

        /* Dark Mode Styles */
        body.dark-mode {
            background: #1a1a1a;
            color: #e0e0e0;
        }

        .dark-mode .sidebar {
            background: linear-gradient(180deg, #1e1e1e 0%, #2d2d2d 100%);
        }

        .dark-mode .main-header {
            background: #2d2d2d;
            border-bottom-color: #404040;
        }

        .dark-mode .card {
            background: #2d2d2d;
            box-shadow: 0 4px 20px rgba(0,0,0,0.3);
        }

        .dark-mode .form-group input,
        .dark-mode .form-group select,
        .dark-mode .form-group textarea {
            background: #404040;
            border-color: #555;
            color: #e0e0e0;
        }

        .dark-mode .stat-card {
            background: #2d2d2d;
            border-top-color: #3498db;
        }

        .dark-mode .pagination-btn {
            background: #404040;
            border-color: #555;
            color: #e0e0e0;
        }

        .dark-mode .pagination-btn:hover {
            background: #555;
        }

        /* Theme Toggle Button */
        .theme-toggle {
            position: fixed;
            top: 20px;
            left: 100px;
            background: #3498db;
            color: white;
            border: none;
            padding: 10px;
            border-radius: 50%;
            cursor: pointer;
            z-index: 1001;
            transition: all 0.3s;
            width: 40px;
            height: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .theme-toggle:hover {
            background: #2980b9;
            transform: scale(1.1);
        }

        /* Notification Styles */
        .notification-container {
            position: fixed;
            top: 20px;
            left: 20px;
            z-index: 1002;
            max-width: 300px;
        }

        .notification {
            background: white;
            border-left: 4px solid #3498db;
            padding: 15px;
            margin-bottom: 10px;
            border-radius: 4px;
            box-shadow: 0 4px 12px rgba(0,0,0,0.15);
            animation: slideIn 0.3s ease;
        }

        .notification.warning {
            border-left-color: #f39c12;
            background: #fff3cd;
        }

        .notification.danger {
            border-left-color: #e74c3c;
            background: #f8d7da;
        }

        @keyframes slideIn {
            from { transform: translateX(-100%); opacity: 0; }
            to { transform: translateX(0); opacity: 1; }
        }

        /* Chart Container */
        .chart-container {
            background: white;
            border-radius: 12px;
            padding: 20px;
            margin: 20px 0;
            box-shadow: 0 4px 20px rgba(0,0,0,0.08);
        }

        .dark-mode .chart-container {
            background: #2d2d2d;
        }

        /* Advanced Search */
        .advanced-search {
            background: #f8f9fa;
            border-radius: 8px;
            padding: 20px;
            margin: 20px 0;
            border: 1px solid #e9ecef;
        }

        .dark-mode .advanced-search {
            background: #404040;
            border-color: #555;
        }

        /* Responsive Design */
        @media (max-width: 768px) {
            .sidebar {
                transform: translateX(100%);
            }

            .sidebar.show {
                transform: translateX(0);
            }

            .main-container {
                margin-right: 0;
            }

            .form-row {
                flex-direction: column;
            }

            .stats-grid {
                grid-template-columns: 1fr;
            }

            .theme-toggle {
                top: 10px;
                left: 10px;
            }

            .notification-container {
                left: 10px;
                right: 10px;
                max-width: none;
            }
        }
    </style>
</head>
<body>
    <!-- Theme Toggle Button -->
    <button class="theme-toggle" onclick="toggleTheme()" title="تبديل الوضع الليلي">
        🌙
    </button>

    <!-- Notification Container -->
    <div class="notification-container" id="notificationContainer"></div>

    <!-- Sidebar -->
    <div class="sidebar" id="sidebar">
        <div class="sidebar-header">
            <button class="sidebar-toggle" onclick="toggleSidebar()">☰</button>
            <div class="sidebar-title">نظام الإجازات</div>
            <div class="sidebar-subtitle">إدارة متطورة</div>
        </div>
        
        <nav class="sidebar-nav">
            <div class="nav-item active" onclick="showSection('dashboard')">
                <span class="nav-icon">📊</span>
                <span class="nav-text">لوحة التحكم</span>
            </div>
            <div class="nav-item" onclick="showSection('employees')">
                <span class="nav-icon">👥</span>
                <span class="nav-text">إدارة الموظفين</span>
            </div>
            <div class="nav-item" onclick="showSection('leaves')">
                <span class="nav-icon">📅</span>
                <span class="nav-text">إدارة الإجازات</span>
            </div>
            <div class="nav-item" onclick="showSection('reports')">
                <span class="nav-icon">📈</span>
                <span class="nav-text">التقارير</span>
            </div>
            <div class="nav-item" onclick="showSection('settings')">
                <span class="nav-icon">⚙️</span>
                <span class="nav-text">الإعدادات</span>
            </div>
        </nav>
    </div>

    <!-- Main Container -->
    <div class="main-container" id="mainContainer">
        <!-- Header -->
        <div class="main-header">
            <div class="save-indicator" id="saveIndicator">محفوظ تلقائياً</div>
            <h1 class="main-title" id="pageTitle">لوحة التحكم</h1>
            <p class="main-subtitle" id="pageSubtitle">نظرة عامة على إحصائيات الإجازات</p>
        </div>

        <!-- Main Content -->
        <div class="main-content">
            <div id="alertContainer"></div>
            
            <!-- Dashboard Section -->
            <div id="dashboard" class="section active">
                <!-- Stats will be loaded here -->
            </div>

            <!-- Employees Section -->
            <div id="employees" class="section">
                <!-- Employee management will be loaded here -->
            </div>

            <!-- Leaves Section -->
            <div id="leaves" class="section">
                <!-- Leave management will be loaded here -->
            </div>

            <!-- Reports Section -->
            <div id="reports" class="section">
                <!-- Reports will be loaded here -->
            </div>

            <!-- Settings Section -->
            <div id="settings" class="section">
                <!-- Settings will be loaded here -->
            </div>
        </div>
    </div>

    <script>
        // Polyfills for older browsers
        if (!Array.prototype.includes) {
            Array.prototype.includes = function(searchElement) {
                return this.indexOf(searchElement) !== -1;
            };
        }

        if (!Array.prototype.find) {
            Array.prototype.find = function(predicate) {
                for (let i = 0; i < this.length; i++) {
                    if (predicate(this[i], i, this)) {
                        return this[i];
                    }
                }
                return undefined;
            };
        }

        if (!Array.prototype.findIndex) {
            Array.prototype.findIndex = function(predicate) {
                for (let i = 0; i < this.length; i++) {
                    if (predicate(this[i], i, this)) {
                        return i;
                    }
                }
                return -1;
            };
        }

        if (!Object.assign) {
            Object.assign = function(target) {
                for (let i = 1; i < arguments.length; i++) {
                    let source = arguments[i];
                    for (let key in source) {
                        if (source.hasOwnProperty(key)) {
                            target[key] = source[key];
                        }
                    }
                }
                return target;
            };
        }

        // Global variables
        let employees = [];
        let filteredEmployees = [];
        let editingIndex = -1;
        let currentEmployeeIndex = -1;
        let currentSection = 'dashboard';

        // Pagination variables
        let currentPage = 1;
        let itemsPerPage = 50;
        let totalPages = 1;

        // Debouncing variables
        let searchTimeout;

        // Theme variables
        let isDarkMode = false;

        // Notification system variables
        let notificationQueue = [];
        let notificationTimer;

        // Utility functions
        function debounce(func, delay) {
            return function(...args) {
                clearTimeout(searchTimeout);
                searchTimeout = setTimeout(() => func.apply(this, args), delay);
            };
        }

        function calculatePagination() {
            const totalItems = filteredEmployees.length || employees.length;
            totalPages = Math.ceil(totalItems / itemsPerPage);
            if (currentPage > totalPages) currentPage = 1;
        }

        function getPaginatedData() {
            const data = filteredEmployees.length > 0 || hasActiveFilters() ? filteredEmployees : employees;
            const startIndex = (currentPage - 1) * itemsPerPage;
            const endIndex = startIndex + itemsPerPage;
            return data.slice(startIndex, endIndex);
        }

        function createPaginationControls() {
            if (totalPages <= 1) return '';

            let paginationHTML = '<div class="pagination-container">';

            // Previous button
            paginationHTML += `<button class="pagination-btn" onclick="changePage(${currentPage - 1})" ${currentPage === 1 ? 'disabled' : ''}>السابق</button>`;

            // Page numbers
            for (let i = 1; i <= totalPages; i++) {
                if (i === 1 || i === totalPages || (i >= currentPage - 2 && i <= currentPage + 2)) {
                    paginationHTML += `<button class="pagination-btn ${i === currentPage ? 'active' : ''}" onclick="changePage(${i})">${i}</button>`;
                } else if (i === currentPage - 3 || i === currentPage + 3) {
                    paginationHTML += '<span class="pagination-info">...</span>';
                }
            }

            // Next button
            paginationHTML += `<button class="pagination-btn" onclick="changePage(${currentPage + 1})" ${currentPage === totalPages ? 'disabled' : ''}>التالي</button>`;

            // Info
            const startItem = (currentPage - 1) * itemsPerPage + 1;
            const endItem = Math.min(currentPage * itemsPerPage, filteredEmployees.length || employees.length);
            const totalItems = filteredEmployees.length || employees.length;
            paginationHTML += `<div class="pagination-info">عرض ${startItem}-${endItem} من ${totalItems}</div>`;

            paginationHTML += '</div>';
            return paginationHTML;
        }

        function changePage(page) {
            if (page < 1 || page > totalPages) return;
            currentPage = page;
            updateTable();
        }

        // Theme functions
        function toggleTheme() {
            isDarkMode = !isDarkMode;
            document.body.classList.toggle('dark-mode', isDarkMode);

            const themeButton = document.querySelector('.theme-toggle');
            themeButton.textContent = isDarkMode ? '☀️' : '🌙';
            themeButton.title = isDarkMode ? 'تبديل للوضع النهاري' : 'تبديل للوضع الليلي';

            // Save theme preference
            localStorage.setItem('darkMode', isDarkMode);

            showNotification(isDarkMode ? 'تم تفعيل الوضع الليلي' : 'تم تفعيل الوضع النهاري', 'info');
        }

        function loadTheme() {
            const savedTheme = localStorage.getItem('darkMode');
            if (savedTheme === 'true') {
                isDarkMode = true;
                document.body.classList.add('dark-mode');
                const themeButton = document.querySelector('.theme-toggle');
                themeButton.textContent = '☀️';
                themeButton.title = 'تبديل للوضع النهاري';
            }
        }

        // Enhanced notification system
        function showNotification(message, type = 'info', duration = 5000) {
            const container = document.getElementById('notificationContainer');
            const notification = document.createElement('div');
            notification.className = `notification ${type}`;
            notification.innerHTML = `
                <div style="display: flex; justify-content: space-between; align-items: center;">
                    <span>${message}</span>
                    <button onclick="this.parentElement.parentElement.remove()" style="background: none; border: none; font-size: 18px; cursor: pointer; color: #999;">×</button>
                </div>
            `;

            container.appendChild(notification);

            // Auto remove after duration
            setTimeout(() => {
                if (notification.parentElement) {
                    notification.remove();
                }
            }, duration);
        }

        // Leave balance monitoring
        function checkLeaveBalances() {
            employees.forEach(emp => {
                const totalAvailable = emp.annualLeave + emp.carriedOverLeave;
                const remaining = totalAvailable - emp.usedAnnual;

                if (remaining <= 0) {
                    showNotification(`⚠️ الموظف ${emp.name} نفد رصيد إجازاته`, 'danger', 8000);
                } else if (remaining <= 5) {
                    showNotification(`⚠️ الموظف ${emp.name} رصيده منخفض: ${remaining} يوم متبقي`, 'warning', 6000);
                }
            });
        }

        // Advanced search functions
        function toggleAdvancedSearch() {
            const advancedSearch = document.getElementById('advancedSearch');
            const toggleButton = document.getElementById('advancedSearchToggle');

            if (advancedSearch.style.display === 'none') {
                advancedSearch.style.display = 'block';
                toggleButton.textContent = '🔼 إخفاء البحث المتقدم';
            } else {
                advancedSearch.style.display = 'none';
                toggleButton.textContent = '🔍 البحث المتقدم';
            }
        }

        function clearAdvancedSearch() {
            document.getElementById('hireDateFrom').value = '';
            document.getElementById('hireDateTo').value = '';
            document.getElementById('serviceYearsFrom').value = '';
            document.getElementById('serviceYearsTo').value = '';
            document.getElementById('remainingFrom').value = '';
            document.getElementById('remainingTo').value = '';
            filterEmployees();
        }

        // Simple chart drawing functions
        function drawPieChart(canvas, data, colors) {
            const ctx = canvas.getContext('2d');
            const centerX = canvas.width / 2;
            const centerY = canvas.height / 2;
            const radius = Math.min(centerX, centerY) - 20;

            ctx.clearRect(0, 0, canvas.width, canvas.height);

            let total = data.reduce((sum, item) => sum + item.value, 0);
            if (total === 0) {
                ctx.fillStyle = '#ddd';
                ctx.fillText('لا توجد بيانات', centerX - 30, centerY);
                return;
            }

            let currentAngle = -Math.PI / 2;

            data.forEach((item, index) => {
                const sliceAngle = (item.value / total) * 2 * Math.PI;

                ctx.beginPath();
                ctx.moveTo(centerX, centerY);
                ctx.arc(centerX, centerY, radius, currentAngle, currentAngle + sliceAngle);
                ctx.closePath();
                ctx.fillStyle = colors[index % colors.length];
                ctx.fill();
                ctx.strokeStyle = '#fff';
                ctx.lineWidth = 2;
                ctx.stroke();

                // Add labels
                const labelAngle = currentAngle + sliceAngle / 2;
                const labelX = centerX + Math.cos(labelAngle) * (radius * 0.7);
                const labelY = centerY + Math.sin(labelAngle) * (radius * 0.7);

                ctx.fillStyle = '#fff';
                ctx.font = '12px Arial';
                ctx.textAlign = 'center';
                ctx.fillText(item.value, labelX, labelY);

                currentAngle += sliceAngle;
            });

            // Add legend
            let legendY = 10;
            data.forEach((item, index) => {
                ctx.fillStyle = colors[index % colors.length];
                ctx.fillRect(10, legendY, 15, 15);
                ctx.fillStyle = '#333';
                ctx.font = '12px Arial';
                ctx.textAlign = 'left';
                ctx.fillText(`${item.label}: ${item.value}`, 30, legendY + 12);
                legendY += 20;
            });
        }

        function updateCharts() {
            const departmentCanvas = document.getElementById('departmentChart');
            const statusCanvas = document.getElementById('leaveStatusChart');

            if (employees.length === 0) {
                // Show placeholder for empty data
                if (departmentCanvas) {
                    const ctx = departmentCanvas.getContext('2d');
                    ctx.clearRect(0, 0, departmentCanvas.width, departmentCanvas.height);
                    ctx.fillStyle = '#f8f9fa';
                    ctx.fillRect(0, 0, departmentCanvas.width, departmentCanvas.height);
                    ctx.fillStyle = '#6c757d';
                    ctx.font = '16px Arial';
                    ctx.textAlign = 'center';
                    ctx.fillText('لا توجد بيانات للعرض', departmentCanvas.width / 2, departmentCanvas.height / 2 - 10);
                    ctx.font = '12px Arial';
                    ctx.fillText('أضف موظفين لرؤية التوزيع', departmentCanvas.width / 2, departmentCanvas.height / 2 + 10);
                }

                if (statusCanvas) {
                    const ctx = statusCanvas.getContext('2d');
                    ctx.clearRect(0, 0, statusCanvas.width, statusCanvas.height);
                    ctx.fillStyle = '#f8f9fa';
                    ctx.fillRect(0, 0, statusCanvas.width, statusCanvas.height);
                    ctx.fillStyle = '#6c757d';
                    ctx.font = '16px Arial';
                    ctx.textAlign = 'center';
                    ctx.fillText('لا توجد بيانات للعرض', statusCanvas.width / 2, statusCanvas.height / 2 - 10);
                    ctx.font = '12px Arial';
                    ctx.fillText('أضف موظفين لرؤية الحالات', statusCanvas.width / 2, statusCanvas.height / 2 + 10);
                }
                return;
            }

            // Department distribution chart
            const departmentData = {};
            employees.forEach(emp => {
                departmentData[emp.department] = (departmentData[emp.department] || 0) + 1;
            });

            const departmentChartData = Object.entries(departmentData).map(([dept, count]) => ({
                label: dept,
                value: count
            }));

            if (departmentCanvas) {
                drawPieChart(departmentCanvas, departmentChartData, ['#3498db', '#e74c3c', '#f39c12', '#27ae60', '#9b59b6', '#1abc9c']);
            }

            // Leave status chart
            const statusData = { normal: 0, low: 0, depleted: 0 };
            employees.forEach(emp => {
                const status = getEmployeeStatus(emp);
                if (status === 'طبيعي') statusData.normal++;
                else if (status === 'رصيد منخفض') statusData.low++;
                else if (status === 'نفد الرصيد') statusData.depleted++;
            });

            const statusChartData = [
                { label: 'طبيعي', value: statusData.normal },
                { label: 'رصيد منخفض', value: statusData.low },
                { label: 'نفد الرصيد', value: statusData.depleted }
            ];

            if (statusCanvas) {
                drawPieChart(statusCanvas, statusChartData, ['#27ae60', '#f39c12', '#e74c3c']);
            }
        }

        // User guide and help functions
        function startUserGuide() {
            const guideContent = document.getElementById('guideContent');
            guideContent.style.display = 'block';
            guideContent.innerHTML = `
                <div style="background: linear-gradient(135deg, #667eea, #764ba2); color: white; padding: 20px; border-radius: 12px; margin-bottom: 20px;">
                    <h3 style="margin: 0 0 15px 0;">🎯 مرحباً بك في نظام إدارة الإجازات المتطور</h3>
                    <p style="margin: 0; font-size: 16px;">سنأخذك في جولة سريعة لتتعرف على جميع ميزات النظام</p>
                </div>

                <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 20px;">
                    <div style="background: #f8f9fa; padding: 20px; border-radius: 8px; border-left: 4px solid #3498db;">
                        <h4 style="color: #2c3e50; margin-bottom: 10px;">📊 لوحة التحكم</h4>
                        <p>عرض الإحصائيات العامة والرسوم البيانية التفاعلية لحالة الإجازات</p>
                        <button class="btn btn-small btn-primary" onclick="showSection('dashboard')">انتقل إلى لوحة التحكم</button>
                    </div>

                    <div style="background: #f8f9fa; padding: 20px; border-radius: 8px; border-left: 4px solid #27ae60;">
                        <h4 style="color: #2c3e50; margin-bottom: 10px;">👥 إدارة الموظفين</h4>
                        <p>إضافة وتعديل بيانات الموظفين مع البحث المتقدم والفلترة</p>
                        <button class="btn btn-small btn-success" onclick="showSection('employees')">انتقل إلى إدارة الموظفين</button>
                    </div>

                    <div style="background: #f8f9fa; padding: 20px; border-radius: 8px; border-left: 4px solid #f39c12;">
                        <h4 style="color: #2c3e50; margin-bottom: 10px;">📅 إدارة الإجازات</h4>
                        <p>إدارة شاملة للإجازات بشاشة كاملة مع تتبع الرصيد المرحل</p>
                        <button class="btn btn-small btn-warning" onclick="showSection('leaves')">انتقل إلى إدارة الإجازات</button>
                    </div>

                    <div style="background: #f8f9fa; padding: 20px; border-radius: 8px; border-left: 4px solid #e74c3c;">
                        <h4 style="color: #2c3e50; margin-bottom: 10px;">📈 التقارير</h4>
                        <p>تصدير البيانات وإنشاء تقارير مفصلة بصيغ مختلفة</p>
                        <button class="btn btn-small btn-danger" onclick="showSection('reports')">انتقل إلى التقارير</button>
                    </div>
                </div>

                <div style="margin-top: 20px; padding: 15px; background: #e8f5e8; border-radius: 8px;">
                    <h4 style="color: #2d5a2d; margin-bottom: 10px;">💡 نصائح سريعة:</h4>
                    <ul style="margin: 0; color: #2d5a2d;">
                        <li>استخدم <strong>Ctrl + /</strong> لعرض اختصارات لوحة المفاتيح</li>
                        <li>استخدم <strong>Ctrl + S</strong> للحفظ السريع</li>
                        <li>استخدم <strong>Ctrl + F</strong> للبحث السريع</li>
                        <li>انقر على 🌙 لتبديل الوضع الليلي</li>
                    </ul>
                </div>
            `;
        }

        function showKeyboardShortcuts() {
            const guideContent = document.getElementById('guideContent');
            guideContent.style.display = 'block';
            guideContent.innerHTML = `
                <div style="background: #2c3e50; color: white; padding: 20px; border-radius: 12px; margin-bottom: 20px;">
                    <h3 style="margin: 0 0 15px 0;">⌨️ اختصارات لوحة المفاتيح</h3>
                    <p style="margin: 0;">اختصارات لتسريع عملك في النظام</p>
                </div>

                <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 15px;">
                    <div style="background: white; padding: 15px; border-radius: 8px; border: 1px solid #ddd;">
                        <h4 style="color: #3498db; margin-bottom: 10px;">التنقل العام</h4>
                        <div style="font-family: monospace; font-size: 14px;">
                            <div><strong>Ctrl + 1</strong> - لوحة التحكم</div>
                            <div><strong>Ctrl + 2</strong> - إدارة الموظفين</div>
                            <div><strong>Ctrl + 3</strong> - إدارة الإجازات</div>
                            <div><strong>Ctrl + 4</strong> - التقارير</div>
                            <div><strong>Ctrl + 5</strong> - الإعدادات</div>
                        </div>
                    </div>

                    <div style="background: white; padding: 15px; border-radius: 8px; border: 1px solid #ddd;">
                        <h4 style="color: #27ae60; margin-bottom: 10px;">العمليات</h4>
                        <div style="font-family: monospace; font-size: 14px;">
                            <div><strong>Ctrl + S</strong> - حفظ سريع</div>
                            <div><strong>Ctrl + N</strong> - موظف جديد</div>
                            <div><strong>Ctrl + F</strong> - بحث سريع</div>
                            <div><strong>Ctrl + E</strong> - تصدير البيانات</div>
                            <div><strong>Ctrl + T</strong> - الوضع الليلي</div>
                        </div>
                    </div>

                    <div style="background: white; padding: 15px; border-radius: 8px; border: 1px solid #ddd;">
                        <h4 style="color: #f39c12; margin-bottom: 10px;">المساعدة</h4>
                        <div style="font-family: monospace; font-size: 14px;">
                            <div><strong>Ctrl + /</strong> - عرض المساعدة</div>
                            <div><strong>Ctrl + H</strong> - دليل المستخدم</div>
                            <div><strong>F1</strong> - نصائح وحيل</div>
                            <div><strong>Esc</strong> - إغلاق النوافذ</div>
                        </div>
                    </div>
                </div>

                <div style="margin-top: 20px; padding: 15px; background: #fff3cd; border-radius: 8px; border: 1px solid #ffeaa7;">
                    <p style="margin: 0; color: #856404;"><strong>ملاحظة:</strong> جميع الاختصارات تعمل في أي مكان في النظام</p>
                </div>
            `;
        }

        function showTips() {
            const guideContent = document.getElementById('guideContent');
            guideContent.style.display = 'block';
            guideContent.innerHTML = `
                <div style="background: linear-gradient(135deg, #fd79a8, #fdcb6e); color: white; padding: 20px; border-radius: 12px; margin-bottom: 20px;">
                    <h3 style="margin: 0 0 15px 0;">💡 نصائح وحيل لاستخدام النظام بكفاءة</h3>
                </div>

                <div style="display: grid; gap: 15px;">
                    <div style="background: white; padding: 20px; border-radius: 8px; border-left: 4px solid #00b894;">
                        <h4 style="color: #00b894; margin-bottom: 10px;">🎯 نصائح الإنتاجية</h4>
                        <ul style="margin: 0; line-height: 1.6;">
                            <li>استخدم البحث المتقدم لفلترة الموظفين حسب معايير متعددة</li>
                            <li>اضغط على أيقونة 📅 بجانب اسم الموظف للانتقال مباشرة لإدارة إجازاته</li>
                            <li>استخدم الـ Pagination عند وجود أكثر من 50 موظف لتحسين الأداء</li>
                            <li>فعل الوضع الليلي للعمل المريح في الإضاءة المنخفضة</li>
                        </ul>
                    </div>

                    <div style="background: white; padding: 20px; border-radius: 8px; border-left: 4px solid #6c5ce7;">
                        <h4 style="color: #6c5ce7; margin-bottom: 10px;">🔒 نصائح الأمان</h4>
                        <ul style="margin: 0; line-height: 1.6;">
                            <li>يتم حفظ البيانات تلقائياً كل 30 ثانية مع تشفير محسن</li>
                            <li>يحتفظ النظام بنسخ احتياطية لآخر 7 أيام تلقائياً</li>
                            <li>تحقق من التنبيهات الأمنية في حالة تعديل البيانات</li>
                            <li>صدر نسخة احتياطية بانتظام من قسم التقارير</li>
                        </ul>
                    </div>

                    <div style="background: white; padding: 20px; border-radius: 8px; border-left: 4px solid #fd79a8;">
                        <h4 style="color: #fd79a8; margin-bottom: 10px;">📊 نصائح التقارير</h4>
                        <ul style="margin: 0; line-height: 1.6;">
                            <li>استخدم تصدير CSV لتحليل البيانات في Excel</li>
                            <li>راجع الرسوم البيانية في لوحة التحكم لفهم توزيع الإجازات</li>
                            <li>استخدم التقرير المفصل لمراجعة الإحصائيات حسب الأقسام</li>
                            <li>راقب التنبيهات للموظفين ذوي الرصيد المنخفض</li>
                        </ul>
                    </div>
                </div>
            `;
        }

        // Navigation functions
        function toggleSidebar() {
            const sidebar = document.getElementById('sidebar');
            const mainContainer = document.getElementById('mainContainer');

            sidebar.classList.toggle('collapsed');
            mainContainer.classList.toggle('expanded');
        }

        function showSection(sectionName) {
            // Hide all sections
            document.querySelectorAll('.section').forEach(section => {
                section.classList.remove('active');
            });

            // Remove active class from all nav items
            document.querySelectorAll('.nav-item').forEach(item => {
                item.classList.remove('active');
            });

            // Show selected section
            document.getElementById(sectionName).classList.add('active');

            // Add active class to correct nav item
            const navItems = document.querySelectorAll('.nav-item');
            navItems.forEach(item => {
                const itemText = item.textContent.trim();
                if ((sectionName === 'dashboard' && itemText.includes('لوحة التحكم')) ||
                    (sectionName === 'employees' && itemText.includes('إدارة الموظفين')) ||
                    (sectionName === 'leaves' && itemText.includes('إدارة الإجازات')) ||
                    (sectionName === 'reports' && itemText.includes('التقارير')) ||
                    (sectionName === 'settings' && itemText.includes('الإعدادات'))) {
                    item.classList.add('active');
                }
            });

            // Update page title and subtitle
            updatePageHeader(sectionName);

            // Load section content
            loadSectionContent(sectionName);

            // Special handling for dashboard to ensure proper loading
            if (sectionName === 'dashboard') {
                setTimeout(() => {
                    updateStats();
                    updateQuickEmployeeView();
                    updateCharts();
                }, 100);
            }

            currentSection = sectionName;
        }

        function updatePageHeader(sectionName) {
            const titles = {
                dashboard: { title: 'لوحة التحكم', subtitle: 'نظرة عامة على إحصائيات الإجازات' },
                employees: { title: 'إدارة الموظفين', subtitle: 'إضافة وتعديل بيانات الموظفين' },
                leaves: { title: 'إدارة الإجازات', subtitle: 'إدارة شاملة لإجازات الموظفين' },
                reports: { title: 'التقارير', subtitle: 'تقارير وإحصائيات مفصلة' },
                settings: { title: 'الإعدادات', subtitle: 'إعدادات النظام والبيانات' }
            };
            
            document.getElementById('pageTitle').textContent = titles[sectionName].title;
            document.getElementById('pageSubtitle').textContent = titles[sectionName].subtitle;
        }

        // Load section content
        function loadSectionContent(sectionName) {
            const sectionElement = document.getElementById(sectionName);

            switch(sectionName) {
                case 'dashboard':
                    loadDashboard();
                    break;
                case 'employees':
                    loadEmployeesSection();
                    break;
                case 'leaves':
                    loadLeavesSection();
                    break;
                case 'reports':
                    loadReportsSection();
                    break;
                case 'settings':
                    loadSettingsSection();
                    break;
            }
        }

        function loadDashboard() {
            const dashboardElement = document.getElementById('dashboard');

            // Create dashboard content with loading indicators
            dashboardElement.innerHTML = `
                <div class="stats-grid">
                    <div class="stat-card">
                        <h3>إجمالي الموظفين</h3>
                        <div class="number" id="totalEmployees">⏳</div>
                        <div class="change">موظف نشط</div>
                    </div>
                    <div class="stat-card">
                        <h3>إجمالي الرصيد المرحل</h3>
                        <div class="number" id="totalCarriedOver">⏳</div>
                        <div class="change">يوم مرحل</div>
                    </div>
                    <div class="stat-card">
                        <h3>الإجازات المستخدمة</h3>
                        <div class="number" id="totalUsedLeaves">⏳</div>
                        <div class="change">يوم مستخدم</div>
                    </div>
                    <div class="stat-card">
                        <h3>الإجازات المتبقية</h3>
                        <div class="number" id="totalRemainingLeaves">⏳</div>
                        <div class="change">يوم متبقي</div>
                    </div>
                    <div class="stat-card">
                        <h3>متوسط الاستخدام</h3>
                        <div class="number" id="averageUsage">⏳</div>
                        <div class="change">من إجمالي الرصيد</div>
                    </div>
                </div>

                <!-- Charts Section -->
                <div class="chart-container">
                    <h3 style="margin-bottom: 20px; color: #2c3e50;">📊 الرسوم البيانية</h3>
                    <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 20px;">
                        <div>
                            <h4 style="text-align: center; margin-bottom: 15px;">توزيع الموظفين حسب الأقسام</h4>
                            <canvas id="departmentChart" width="300" height="200" style="border: 1px solid #e9ecef; border-radius: 8px;"></canvas>
                        </div>
                        <div>
                            <h4 style="text-align: center; margin-bottom: 15px;">حالات رصيد الإجازات</h4>
                            <canvas id="leaveStatusChart" width="300" height="200" style="border: 1px solid #e9ecef; border-radius: 8px;"></canvas>
                        </div>
                    </div>
                </div>

                <div class="card">
                    <div class="card-header">
                        <h3 class="card-title">📊 نظرة سريعة على الموظفين</h3>
                    </div>
                    <div class="card-body">
                        <div id="quickEmployeeView">
                            <div style="text-align: center; padding: 20px;">
                                <div style="font-size: 24px; margin-bottom: 10px;">⏳</div>
                                <p style="color: #7f8c8d;">جاري تحميل بيانات الموظفين...</p>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Quick Actions Section -->
                <div class="card">
                    <div class="card-header">
                        <h3 class="card-title">🚀 إجراءات سريعة</h3>
                    </div>
                    <div class="card-body">
                        <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 15px;">
                            <button class="btn btn-primary btn-large" onclick="showSection('employees')" style="padding: 15px;">
                                👥 إدارة الموظفين
                            </button>
                            <button class="btn btn-success btn-large" onclick="showSection('leaves')" style="padding: 15px;">
                                📅 إدارة الإجازات
                            </button>
                            <button class="btn btn-warning btn-large" onclick="showSection('reports')" style="padding: 15px;">
                                📊 التقارير
                            </button>
                            <button class="btn btn-secondary btn-large" onclick="loadTestData()" style="padding: 15px;">
                                🧪 بيانات تجريبية
                            </button>
                        </div>
                    </div>
                </div>
            `;

            // Update content immediately
            setTimeout(() => {
                updateStats();
                updateQuickEmployeeView();
                updateCharts();
            }, 100);
        }

        function loadEmployeesSection() {
            const employeesElement = document.getElementById('employees');
            employeesElement.innerHTML = `
                <div class="card">
                    <div class="card-header">
                        <h3 class="card-title">➕ إضافة موظف جديد</h3>
                    </div>
                    <div class="card-body">
                        <div class="form-row">
                            <div class="form-group">
                                <label>اسم الموظف *</label>
                                <input type="text" id="employeeName" placeholder="أدخل اسم الموظف" required
                                       oninput="validateTextInput(this, /^[\\u0600-\\u06FFa-zA-Z\\s]+$/, 'اسم الموظف يجب أن يحتوي على أحرف عربية أو إنجليزية فقط')"
                                       title="أدخل اسم الموظف (أحرف عربية أو إنجليزية فقط)">
                            </div>
                            <div class="form-group">
                                <label>الرقم الوظيفي *</label>
                                <input type="text" id="employeeId" placeholder="أدخل الرقم الوظيفي" required
                                       oninput="validateTextInput(this, /^[a-zA-Z0-9]+$/, 'الرقم الوظيفي يجب أن يحتوي على أرقام وأحرف إنجليزية فقط')"
                                       title="أدخل الرقم الوظيفي (أرقام وأحرف إنجليزية فقط)">
                            </div>
                            <div class="form-group">
                                <label>القسم *</label>
                                <input type="text" id="department" placeholder="أدخل القسم" required
                                       oninput="validateTextInput(this, /^[\\u0600-\\u06FFa-zA-Z\\s]+$/, 'اسم القسم يجب أن يحتوي على أحرف عربية أو إنجليزية فقط')"
                                       title="أدخل اسم القسم (أحرف عربية أو إنجليزية فقط)">
                            </div>
                            <div class="form-group">
                                <label>تاريخ التوظيف *</label>
                                <input type="date" id="hireDate" required
                                       onchange="validateDateInput(this, false)"
                                       title="أدخل تاريخ التوظيف">
                            </div>
                        </div>

                        <div class="form-row">
                            <div class="form-group">
                                <label>الرصيد السنوي (يوم)</label>
                                <input type="number" id="annualLeave" value="30" min="0" max="365" step="1"
                                       oninput="validateNumberInput(this, 0, 365)"
                                       title="أدخل رقم بين 0 و 365">
                            </div>
                            <div class="form-group">
                                <label>الرصيد المرحل (يوم)</label>
                                <input type="number" id="carriedOverLeave" value="0" min="0" max="365" step="1"
                                       oninput="validateNumberInput(this, 0, 365)"
                                       title="أدخل رقم بين 0 و 365 - الرصيد المتبقي من السنوات السابقة">
                            </div>
                            <div class="form-group">
                                <label>إجازة مرضية (يوم)</label>
                                <input type="number" id="sickLeave" value="15" min="0" max="90" step="1"
                                       oninput="validateNumberInput(this, 0, 90)"
                                       title="أدخل رقم بين 0 و 90">
                            </div>
                            <div class="form-group">
                                <label>إجازة طارئة (يوم)</label>
                                <input type="number" id="emergencyLeave" value="5" min="0" max="30" step="1"
                                       oninput="validateNumberInput(this, 0, 30)"
                                       title="أدخل رقم بين 0 و 30">
                            </div>
                        </div>

                        <div class="form-row">
                            <div class="form-group">
                                <button class="btn btn-primary btn-large" onclick="addEmployee()" id="addEmployeeBtn">
                                    ➕ إضافة موظف
                                </button>
                            </div>
                            <div class="form-group">
                                <div style="padding: 15px; background: linear-gradient(135deg, #e8f5e8, #f0f8f0); border-radius: 8px; font-size: 13px; color: #2d5a2d; border: 1px solid #c8e6c9;">
                                    💡 <strong>الرصيد المرحل:</strong> أيام الإجازة المتبقية من السنوات السابقة والتي لم تُستخدم
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="card">
                    <div class="card-header">
                        <h3 class="card-title">🔍 البحث والفلترة</h3>
                    </div>
                    <div class="card-body">
                        <div class="form-row">
                            <div class="form-group">
                                <label>🔍 البحث في الموظفين</label>
                                <input type="text" id="searchInput" class="search-input" placeholder="ابحث بالاسم، الرقم الوظيفي، أو القسم..."
                                       oninput="debouncedFilterEmployees()" title="ابحث في بيانات الموظفين">
                            </div>
                            <div class="form-group">
                                <label>📊 فلترة حسب الحالة</label>
                                <select id="statusFilter" onchange="filterEmployees()">
                                    <option value="">جميع الحالات</option>
                                    <option value="طبيعي">طبيعي</option>
                                    <option value="رصيد منخفض">رصيد منخفض</option>
                                    <option value="نفد الرصيد">نفد الرصيد</option>
                                </select>
                            </div>
                            <div class="form-group">
                                <label>🏢 فلترة حسب القسم</label>
                                <select id="departmentFilter" onchange="filterEmployees()">
                                    <option value="">جميع الأقسام</option>
                                </select>
                            </div>
                            <div class="form-group">
                                <button class="btn btn-warning" onclick="clearFilters()" title="مسح جميع الفلاتر">
                                    🗑️ مسح الفلاتر
                                </button>
                            </div>
                        </div>
                        <div style="margin-top: 15px; font-size: 14px; color: #666;">
                            <span id="filterResults">عرض جميع الموظفين</span>
                        </div>

                        <!-- Advanced Search -->
                        <div class="advanced-search" id="advancedSearch" style="display: none;">
                            <h4 style="margin-bottom: 15px; color: #2c3e50;">🔍 البحث المتقدم</h4>
                            <div class="form-row">
                                <div class="form-group">
                                    <label>تاريخ التوظيف من</label>
                                    <input type="date" id="hireDateFrom" onchange="filterEmployees()">
                                </div>
                                <div class="form-group">
                                    <label>تاريخ التوظيف إلى</label>
                                    <input type="date" id="hireDateTo" onchange="filterEmployees()">
                                </div>
                                <div class="form-group">
                                    <label>سنوات الخدمة (من)</label>
                                    <input type="number" id="serviceYearsFrom" min="0" max="50" onchange="filterEmployees()">
                                </div>
                                <div class="form-group">
                                    <label>سنوات الخدمة (إلى)</label>
                                    <input type="number" id="serviceYearsTo" min="0" max="50" onchange="filterEmployees()">
                                </div>
                            </div>
                            <div class="form-row">
                                <div class="form-group">
                                    <label>الرصيد المتبقي (من)</label>
                                    <input type="number" id="remainingFrom" min="0" onchange="filterEmployees()">
                                </div>
                                <div class="form-group">
                                    <label>الرصيد المتبقي (إلى)</label>
                                    <input type="number" id="remainingTo" min="0" onchange="filterEmployees()">
                                </div>
                                <div class="form-group">
                                    <button class="btn btn-secondary" onclick="clearAdvancedSearch()">مسح البحث المتقدم</button>
                                </div>
                            </div>
                        </div>

                        <div style="margin-top: 10px;">
                            <button class="btn btn-primary btn-small" onclick="toggleAdvancedSearch()">
                                <span id="advancedSearchToggle">🔍 البحث المتقدم</span>
                            </button>
                        </div>
                    </div>
                </div>

                <div class="card">
                    <div class="card-header">
                        <h3 class="card-title">👥 قائمة الموظفين</h3>
                    </div>
                    <div class="card-body">
                        <div class="table-responsive">
                            <table id="employeeTable" style="width: 100%; border-collapse: collapse; background: white; border-radius: 10px; overflow: hidden;">
                                <thead>
                                    <tr style="background: linear-gradient(135deg, #34495e, #2c3e50); color: white;">
                                        <th style="padding: 15px; font-weight: 600; text-align: center;">اسم الموظف</th>
                                        <th style="padding: 15px; font-weight: 600; text-align: center;">الرقم الوظيفي</th>
                                        <th style="padding: 15px; font-weight: 600; text-align: center;">القسم</th>
                                        <th style="padding: 15px; font-weight: 600; text-align: center;">تاريخ التوظيف</th>
                                        <th style="padding: 15px; font-weight: 600; text-align: center;">سنوات الخدمة</th>
                                        <th style="padding: 15px; font-weight: 600; text-align: center;">الرصيد السنوي</th>
                                        <th style="padding: 15px; font-weight: 600; text-align: center;">الرصيد المرحل</th>
                                        <th style="padding: 15px; font-weight: 600; text-align: center;">إجمالي المتاح</th>
                                        <th style="padding: 15px; font-weight: 600; text-align: center;">المستخدم</th>
                                        <th style="padding: 15px; font-weight: 600; text-align: center;">المتبقي</th>
                                        <th style="padding: 15px; font-weight: 600; text-align: center;">إجازة مرضية</th>
                                        <th style="padding: 15px; font-weight: 600; text-align: center;">إجازة طارئة</th>
                                        <th style="padding: 15px; font-weight: 600; text-align: center;">الحالة</th>
                                        <th style="padding: 15px; font-weight: 600; text-align: center;">إجراءات</th>
                                    </tr>
                                </thead>
                                <tbody id="employeeTableBody">
                                </tbody>
                            </table>
                        </div>
                        <div id="paginationControls"></div>
                    </div>
                </div>
            `;

            // Set default hire date to today
            document.getElementById('hireDate').value = new Date().toISOString().split('T')[0];

            updateTable();
            updateDepartmentFilter();
        }

        function loadLeavesSection() {
            const leavesElement = document.getElementById('leaves');
            leavesElement.innerHTML = `
                <div class="card">
                    <div class="card-header">
                        <h3 class="card-title">👥 اختيار الموظف</h3>
                    </div>
                    <div class="card-body">
                        <div class="form-row">
                            <div class="form-group">
                                <label>اختر الموظف لإدارة إجازاته</label>
                                <select id="leaveEmployeeSelect" onchange="selectEmployeeForLeave()" style="font-size: 16px; padding: 15px;">
                                    <option value="">-- اختر موظف --</option>
                                </select>
                            </div>
                        </div>
                        <div id="selectedEmployeeInfo" style="display: none; margin-top: 20px; padding: 20px; background: linear-gradient(135deg, #e8f5e8, #f0f8f0); border-radius: 12px; border: 1px solid #c8e6c9;">
                        </div>
                    </div>
                </div>

                <div id="leaveManagementSection" style="display: none;">
                    <div class="card">
                        <div class="card-header">
                            <h3 class="card-title">➕ إضافة إجازة جديدة</h3>
                        </div>
                        <div class="card-body">
                            <div class="form-row">
                                <div class="form-group">
                                    <label>نوع الإجازة</label>
                                    <select id="leaveType" style="font-size: 16px; padding: 15px;">
                                        <option value="annual">إجازة سنوية</option>
                                        <option value="sick">إجازة مرضية</option>
                                        <option value="emergency">إجازة طارئة</option>
                                    </select>
                                </div>
                                <div class="form-group">
                                    <label>عدد الأيام</label>
                                    <input type="number" id="leaveDays" min="0.5" max="365" step="0.5"
                                           placeholder="أدخل عدد الأيام" style="font-size: 16px; padding: 15px;"
                                           oninput="validateNumberInput(this, 0.5, 365)"
                                           title="أدخل رقم بين 0.5 و 365">
                                </div>
                            </div>

                            <div class="form-row">
                                <div class="form-group">
                                    <label>تاريخ البداية</label>
                                    <input type="date" id="leaveStartDate" style="font-size: 16px; padding: 15px;"
                                           onchange="validateDateInput(this, true); calculateLeaveDays()"
                                           title="أدخل تاريخ بداية الإجازة">
                                </div>
                                <div class="form-group">
                                    <label>تاريخ النهاية</label>
                                    <input type="date" id="leaveEndDate" style="font-size: 16px; padding: 15px;"
                                           onchange="validateDateInput(this, true); calculateLeaveDays()"
                                           title="أدخل تاريخ نهاية الإجازة">
                                </div>
                            </div>

                            <div class="form-row">
                                <div class="form-group">
                                    <label>سبب الإجازة</label>
                                    <textarea id="leaveReason" placeholder="أدخل سبب الإجازة..."
                                              style="font-size: 16px; padding: 15px; min-height: 100px; resize: vertical;"></textarea>
                                </div>
                            </div>

                            <div class="form-row">
                                <div class="form-group">
                                    <button class="btn btn-success btn-large" onclick="addLeaveRecord()" style="font-size: 16px; padding: 15px 30px;">
                                        ➕ إضافة الإجازة
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="card">
                        <div class="card-header">
                            <h3 class="card-title">📋 سجل الإجازات</h3>
                        </div>
                        <div class="card-body">
                            <div id="leaveHistoryContent" style="max-height: 500px; overflow-y: auto;">
                                <p style="text-align: center; color: #7f8c8d; padding: 40px;">
                                    لا توجد إجازات مسجلة لهذا الموظف
                                </p>
                            </div>
                        </div>
                    </div>
                </div>
            `;

            updateLeaveEmployeeSelect();
        }

        function loadReportsSection() {
            const reportsElement = document.getElementById('reports');
            reportsElement.innerHTML = `
                <div class="card">
                    <div class="card-header">
                        <h3 class="card-title">📊 تقارير الإجازات</h3>
                    </div>
                    <div class="card-body">
                        <div class="form-row">
                            <div class="form-group">
                                <button class="btn btn-success btn-large" onclick="exportToCSV()">
                                    📊 تصدير تقرير CSV
                                </button>
                            </div>
                            <div class="form-group">
                                <button class="btn btn-primary btn-large" onclick="exportToJSON()">
                                    💾 تصدير نسخة احتياطية JSON
                                </button>
                            </div>
                            <div class="form-group">
                                <button class="btn btn-secondary btn-large" onclick="generateDetailedReport()">
                                    📋 تقرير مفصل
                                </button>
                            </div>
                        </div>

                        <div id="detailedReportContent" style="margin-top: 30px;">
                            <p style="text-align: center; color: #7f8c8d; padding: 40px;">
                                انقر على "تقرير مفصل" لعرض التقرير الشامل
                            </p>
                        </div>
                    </div>
                </div>
            `;
        }

        function loadSettingsSection() {
            const settingsElement = document.getElementById('settings');
            settingsElement.innerHTML = `
                <div class="card">
                    <div class="card-header">
                        <h3 class="card-title">📚 دليل المستخدم التفاعلي</h3>
                    </div>
                    <div class="card-body">
                        <div class="form-row">
                            <div class="form-group">
                                <button class="btn btn-primary btn-large" onclick="startUserGuide()" style="background: linear-gradient(135deg, #6c5ce7, #a29bfe);">
                                    📖 بدء الجولة التعريفية
                                </button>
                            </div>
                            <div class="form-group">
                                <button class="btn btn-secondary btn-large" onclick="showKeyboardShortcuts()">
                                    ⌨️ اختصارات لوحة المفاتيح
                                </button>
                            </div>
                            <div class="form-group">
                                <button class="btn btn-success btn-large" onclick="showTips()">
                                    💡 نصائح وحيل
                                </button>
                            </div>
                        </div>

                        <div id="guideContent" style="margin-top: 20px; display: none;">
                            <!-- Guide content will be loaded here -->
                        </div>
                    </div>
                </div>

                <div class="card">
                    <div class="card-header">
                        <h3 class="card-title">🧪 البيانات التجريبية والاختبار</h3>
                    </div>
                    <div class="card-body">
                        <div class="form-row">
                            <div class="form-group">
                                <button class="btn btn-large" onclick="loadTestData()"
                                        style="background: linear-gradient(135deg, #9c27b0, #673ab7); font-size: 16px;">
                                    🎯 تحميل بيانات تجريبية
                                </button>
                            </div>
                            <div class="form-group">
                                <button class="btn btn-large" onclick="runComprehensiveTest()"
                                        style="background: linear-gradient(135deg, #ff9800, #f57c00); font-size: 16px;">
                                    🧪 اختبار شامل
                                </button>
                            </div>
                        </div>

                        <div style="margin-top: 20px; padding: 15px; background: #e3f2fd; border-radius: 8px; font-size: 13px; color: #1565c0;">
                            🧪 <strong>البيانات التجريبية تشمل:</strong> 8 موظفين من أقسام مختلفة، أسماء عربية وإنجليزية، قيم متنوعة للرصيد المرحل، حالات مختلفة (طبيعي/منخفض/نفد الرصيد)، سجلات إجازات شاملة.
                        </div>
                    </div>
                </div>

                <div class="card">
                    <div class="card-header">
                        <h3 class="card-title">🗂️ إدارة البيانات</h3>
                    </div>
                    <div class="card-body">
                        <div class="form-row">
                            <div class="form-group">
                                <label for="importFile" class="btn btn-secondary btn-large" style="margin: 0; cursor: pointer;">
                                    📁 استيراد نسخة احتياطية
                                </label>
                                <input type="file" id="importFile" style="display: none;" accept=".json" onchange="importData()">
                            </div>
                            <div class="form-group">
                                <button class="btn btn-warning btn-large" onclick="resetData()">
                                    🔄 إعادة تعيين الإجازات
                                </button>
                            </div>
                            <div class="form-group">
                                <button class="btn btn-danger btn-large" onclick="clearAllData()">
                                    🗑️ مسح جميع البيانات
                                </button>
                            </div>
                        </div>

                        <div style="margin-top: 20px; padding: 15px; background: #f8f9fa; border-radius: 8px; font-size: 14px; color: #666;">
                            💡 <strong>ملاحظة:</strong> يتم حفظ البيانات تلقائياً في التخزين المحلي للمتصفح. البيانات ستبقى محفوظة حتى بعد إغلاق المتصفح.
                        </div>
                    </div>
                </div>
            `;
        }

        // Core functionality from original system

        // Save indicator functions
        function showSaveIndicator(status) {
            const indicator = document.getElementById('saveIndicator');
            if (status === 'saving') {
                indicator.textContent = 'جاري الحفظ...';
                indicator.className = 'save-indicator saving';
            } else {
                indicator.textContent = 'محفوظ تلقائياً';
                indicator.className = 'save-indicator';
            }
        }

        // Alert system
        function showAlert(message, type = 'info') {
            const alertContainer = document.getElementById('alertContainer');
            const alertDiv = document.createElement('div');
            alertDiv.className = `alert alert-${type}`;
            alertDiv.textContent = message;

            alertContainer.appendChild(alertDiv);

            setTimeout(() => {
                alertDiv.remove();
            }, 5000);
        }

        // Enhanced encryption functions
        function simpleEncrypt(text) {
            // Simple base64 encoding with character shifting
            const shifted = text.split('').map(char =>
                String.fromCharCode(char.charCodeAt(0) + 3)
            ).join('');
            return btoa(shifted);
        }

        function simpleDecrypt(encryptedText) {
            try {
                const decoded = atob(encryptedText);
                return decoded.split('').map(char =>
                    String.fromCharCode(char.charCodeAt(0) - 3)
                ).join('');
            } catch (error) {
                return null;
            }
        }

        function compressData(data) {
            // Simple compression by removing unnecessary whitespace
            return JSON.stringify(data).replace(/\s+/g, ' ');
        }

        // Enhanced data storage functions
        function saveData() {
            try {
                const data = {
                    employees: employees,
                    lastSaved: new Date().toISOString(),
                    version: '4.0',
                    checksum: generateChecksum(employees)
                };

                const compressedData = compressData(data);
                const encryptedData = simpleEncrypt(compressedData);

                localStorage.setItem('employeeLeaveSystem', encryptedData);

                // Create automatic backup
                const backupKey = `backup_${new Date().toISOString().split('T')[0]}`;
                localStorage.setItem(backupKey, encryptedData);

                // Keep only last 7 days of backups
                cleanOldBackups();

                showSaveIndicator('saving');

                setTimeout(() => {
                    showSaveIndicator('saved');
                }, 500);

                return JSON.stringify(data, null, 2);
            } catch (error) {
                console.error('Error saving data:', error);
                showAlert('خطأ في حفظ البيانات: ' + error.message, 'danger');
                return null;
            }
        }

        function generateChecksum(data) {
            // Simple checksum generation
            const str = JSON.stringify(data);
            let hash = 0;
            for (let i = 0; i < str.length; i++) {
                const char = str.charCodeAt(i);
                hash = ((hash << 5) - hash) + char;
                hash = hash & hash; // Convert to 32-bit integer
            }
            return hash.toString();
        }

        function cleanOldBackups() {
            const keys = Object.keys(localStorage);
            const backupKeys = keys.filter(key => key.startsWith('backup_'));

            if (backupKeys.length > 7) {
                backupKeys.sort().slice(0, -7).forEach(key => {
                    localStorage.removeItem(key);
                });
            }
        }

        function loadDataFromStorage() {
            try {
                const savedData = localStorage.getItem('employeeLeaveSystem');
                if (savedData) {
                    let data;

                    // Try to decrypt first (new format)
                    const decryptedData = simpleDecrypt(savedData);
                    if (decryptedData) {
                        data = JSON.parse(decryptedData);
                    } else {
                        // Fallback to old format
                        data = JSON.parse(savedData);
                    }

                    if (data.employees && Array.isArray(data.employees)) {
                        // Verify checksum if available
                        if (data.checksum) {
                            const currentChecksum = generateChecksum(data.employees);
                            if (currentChecksum !== data.checksum) {
                                showNotification('⚠️ تم اكتشاف تغيير في البيانات. قد تكون البيانات معدلة.', 'warning', 10000);
                            }
                        }

                        employees = data.employees.map(emp => ({
                            name: emp.name || '',
                            id: emp.id || '',
                            department: emp.department || '',
                            hireDate: emp.hireDate || new Date().toISOString().split('T')[0],
                            annualLeave: emp.annualLeave || 30,
                            carriedOverLeave: emp.carriedOverLeave || 0,
                            sickLeave: emp.sickLeave || 15,
                            emergencyLeave: emp.emergencyLeave || 5,
                            usedAnnual: emp.usedAnnual || 0,
                            usedSick: emp.usedSick || 0,
                            usedEmergency: emp.usedEmergency || 0,
                            leaveHistory: emp.leaveHistory || []
                        }));

                        console.log(`تم تحميل ${employees.length} موظف من التخزين المحلي`);
                        showNotification(`تم تحميل ${employees.length} موظف بنجاح`, 'info', 3000);
                        return true;
                    }
                }
                return false;
            } catch (error) {
                console.error('Error loading data from localStorage:', error);
                showAlert('خطأ في تحميل البيانات المحفوظة: ' + error.message, 'warning');

                // Try to load from backup
                return loadFromBackup();
            }
        }

        function loadFromBackup() {
            try {
                const keys = Object.keys(localStorage);
                const backupKeys = keys.filter(key => key.startsWith('backup_')).sort().reverse();

                for (const backupKey of backupKeys) {
                    try {
                        const backupData = localStorage.getItem(backupKey);
                        const decryptedData = simpleDecrypt(backupData);
                        if (decryptedData) {
                            const data = JSON.parse(decryptedData);
                            if (data.employees && Array.isArray(data.employees)) {
                                employees = data.employees;
                                showNotification(`تم استرداد البيانات من النسخة الاحتياطية: ${backupKey}`, 'info', 5000);
                                return true;
                            }
                        }
                    } catch (error) {
                        continue;
                    }
                }
                return false;
            } catch (error) {
                console.error('Error loading from backup:', error);
                return false;
            }
        }

        function loadData() {
            try {
                const loaded = loadDataFromStorage();

                if (!loaded) {
                    employees = [];
                    console.log('لا توجد بيانات محفوظة مسبقاً');
                } else {
                    console.log(`تم تحميل ${employees.length} موظف بنجاح`);
                }

                filteredEmployees = [...employees];

                // Update components with a small delay to ensure DOM is ready
                setTimeout(() => {
                    updateStats();
                    updateQuickEmployeeView();
                }, 100);

                console.log('Data loading completed successfully');
                return loaded;
            } catch (error) {
                console.error('Error in loadData:', error);
                employees = [];
                filteredEmployees = [];
                showNotification('خطأ في تحميل البيانات. تم البدء بقائمة فارغة.', 'warning', 5000);
                return false;
            }
        }

        // Validation functions
        function validateNumberInput(input, min, max) {
            const value = parseFloat(input.value);

            input.value = input.value.replace(/[^0-9.-]/g, '');

            if (isNaN(value)) {
                input.style.borderColor = '#e74c3c';
                return false;
            }

            if (value < min || value > max) {
                input.style.borderColor = '#f39c12';
                input.title = `القيمة يجب أن تكون بين ${min} و ${max}`;
                return false;
            }

            input.style.borderColor = '#27ae60';
            input.title = '';
            return true;
        }

        function validateDateInput(dateInput, allowFuture = false) {
            const inputDate = new Date(dateInput.value);
            const today = new Date();
            today.setHours(0, 0, 0, 0);

            if (isNaN(inputDate.getTime())) {
                dateInput.style.borderColor = '#e74c3c';
                return false;
            }

            if (!allowFuture && inputDate > today) {
                dateInput.style.borderColor = '#f39c12';
                dateInput.title = 'التاريخ لا يمكن أن يكون في المستقبل';
                return false;
            }

            const fiftyYearsAgo = new Date();
            fiftyYearsAgo.setFullYear(fiftyYearsAgo.getFullYear() - 50);

            if (inputDate < fiftyYearsAgo) {
                dateInput.style.borderColor = '#f39c12';
                dateInput.title = 'التاريخ قديم جداً';
                return false;
            }

            dateInput.style.borderColor = '#27ae60';
            dateInput.title = '';
            return true;
        }

        function validateTextInput(input, pattern, errorMessage) {
            const value = input.value.trim();

            if (!value) {
                input.style.borderColor = '#e74c3c';
                return false;
            }

            if (pattern && !pattern.test(value)) {
                input.style.borderColor = '#f39c12';
                input.title = errorMessage;
                return false;
            }

            input.style.borderColor = '#27ae60';
            input.title = '';
            return true;
        }

        function sanitizeInput(input) {
            if (typeof input !== 'string') return input;

            return input
                .replace(/[<>]/g, '')
                .replace(/javascript:/gi, '')
                .replace(/on\w+=/gi, '')
                .replace(/&/g, '&amp;')
                .replace(/"/g, '&quot;')
                .replace(/'/g, '&#x27;')
                .trim();
        }

        function sanitizeEmployee(employee) {
            return {
                name: sanitizeInput(employee.name),
                id: sanitizeInput(employee.id),
                department: sanitizeInput(employee.department),
                hireDate: employee.hireDate,
                annualLeave: parseInt(employee.annualLeave) || 0,
                carriedOverLeave: parseInt(employee.carriedOverLeave) || 0,
                sickLeave: parseInt(employee.sickLeave) || 0,
                emergencyLeave: parseInt(employee.emergencyLeave) || 0,
                usedAnnual: parseFloat(employee.usedAnnual) || 0,
                usedSick: parseFloat(employee.usedSick) || 0,
                usedEmergency: parseFloat(employee.usedEmergency) || 0,
                leaveHistory: employee.leaveHistory ? employee.leaveHistory.map(record => ({
                    id: record.id,
                    type: sanitizeInput(record.type),
                    typeName: sanitizeInput(record.typeName),
                    days: parseFloat(record.days) || 0,
                    startDate: record.startDate,
                    endDate: record.endDate,
                    reason: sanitizeInput(record.reason),
                    addedDate: record.addedDate
                })) : []
            };
        }

        // Employee management functions
        function calculateYearsOfService(hireDate) {
            const hire = new Date(hireDate);
            const now = new Date();
            const years = Math.floor((now - hire) / (365.25 * 24 * 60 * 60 * 1000));
            return years;
        }

        function getEmployeeStatus(employee) {
            const totalAvailable = employee.annualLeave + employee.carriedOverLeave;
            const remainingTotal = totalAvailable - employee.usedAnnual;
            if (remainingTotal <= 0) return 'نفد الرصيد';
            if (remainingTotal <= 5) return 'رصيد منخفض';
            return 'طبيعي';
        }

        function addEmployee() {
            const name = sanitizeInput(document.getElementById('employeeName').value.trim());
            const id = sanitizeInput(document.getElementById('employeeId').value.trim());
            const department = sanitizeInput(document.getElementById('department').value.trim());
            const hireDate = document.getElementById('hireDate').value;
            const annualLeave = parseInt(document.getElementById('annualLeave').value) || 0;
            const carriedOverLeave = parseInt(document.getElementById('carriedOverLeave').value) || 0;
            const sickLeave = parseInt(document.getElementById('sickLeave').value) || 0;
            const emergencyLeave = parseInt(document.getElementById('emergencyLeave').value) || 0;

            // Enhanced validation
            if (!name || !id || !department || !hireDate) {
                showAlert('يرجى ملء جميع الحقول المطلوبة المميزة بـ *', 'warning');
                return;
            }

            if (!/^[\u0600-\u06FFa-zA-Z\s]+$/.test(name)) {
                showAlert('اسم الموظف يجب أن يحتوي على أحرف عربية أو إنجليزية فقط', 'warning');
                return;
            }

            if (!/^[a-zA-Z0-9]+$/.test(id)) {
                showAlert('الرقم الوظيفي يجب أن يحتوي على أرقام وأحرف إنجليزية فقط', 'warning');
                return;
            }

            if (editingIndex === -1 && employees.some(emp => emp.id === id)) {
                showAlert('الرقم الوظيفي موجود مسبقاً. يرجى استخدام رقم مختلف', 'warning');
                return;
            }

            if (new Date(hireDate) > new Date()) {
                showAlert('تاريخ التوظيف لا يمكن أن يكون في المستقبل', 'warning');
                return;
            }

            if (annualLeave < 0 || carriedOverLeave < 0 || sickLeave < 0 || emergencyLeave < 0) {
                showAlert('قيم الإجازات يجب أن تكون أرقام موجبة', 'warning');
                return;
            }

            if (annualLeave > 365 || carriedOverLeave > 365 || sickLeave > 90 || emergencyLeave > 30) {
                showAlert('قيم الإجازات تبدو غير منطقية. يرجى المراجعة', 'warning');
                return;
            }

            if (carriedOverLeave > annualLeave * 2) {
                showAlert('الرصيد المرحل لا يمكن أن يتجاوز ضعف الرصيد السنوي', 'warning');
                return;
            }

            const employee = sanitizeEmployee({
                name,
                id,
                department,
                hireDate,
                annualLeave,
                carriedOverLeave,
                sickLeave,
                emergencyLeave,
                usedAnnual: 0,
                usedSick: 0,
                usedEmergency: 0,
                leaveHistory: []
            });

            if (editingIndex === -1) {
                employees.push(employee);
                showAlert(`تم إضافة الموظف ${name} بنجاح`, 'success');
            } else {
                employee.leaveHistory = employees[editingIndex].leaveHistory || [];
                employees[editingIndex] = employee;
                showAlert(`تم تحديث بيانات الموظف ${name} بنجاح`, 'success');
                editingIndex = -1;
                document.getElementById('addEmployeeBtn').innerHTML = '➕ إضافة موظف';
            }

            clearForm();
            updateDepartmentFilter();
            filterEmployees();
            updateStats();
            updateQuickEmployeeView();
            saveData();
        }

        function clearForm() {
            document.getElementById('employeeName').value = '';
            document.getElementById('employeeId').value = '';
            document.getElementById('department').value = '';
            document.getElementById('hireDate').value = new Date().toISOString().split('T')[0];
            document.getElementById('annualLeave').value = '30';
            document.getElementById('carriedOverLeave').value = '0';
            document.getElementById('sickLeave').value = '15';
            document.getElementById('emergencyLeave').value = '5';
        }

        function editEmployee(index) {
            const employee = employees[index];
            document.getElementById('employeeName').value = employee.name;
            document.getElementById('employeeId').value = employee.id;
            document.getElementById('department').value = employee.department;
            document.getElementById('hireDate').value = employee.hireDate;
            document.getElementById('annualLeave').value = employee.annualLeave;
            document.getElementById('carriedOverLeave').value = employee.carriedOverLeave || 0;
            document.getElementById('sickLeave').value = employee.sickLeave;
            document.getElementById('emergencyLeave').value = employee.emergencyLeave;

            editingIndex = index;
            document.getElementById('addEmployeeBtn').innerHTML = '🔄 تحديث الموظف';

            // Switch to employees section and scroll to form
            showSection('employees');
            setTimeout(() => {
                document.querySelector('.card').scrollIntoView({ behavior: 'smooth' });
            }, 300);
        }

        function deleteEmployee(index) {
            const employee = employees[index];
            if (confirm(`هل أنت متأكد من حذف الموظف "${employee.name}"؟\nسيتم حذف جميع سجلات الإجازات المرتبطة به.`)) {
                employees.splice(index, 1);
                updateDepartmentFilter();
                filterEmployees();
                updateStats();
                updateQuickEmployeeView();
                saveData();
                showAlert(`تم حذف الموظف "${employee.name}" بنجاح`, 'success');
            }
        }

        // Table and display functions
        function updateTable() {
            const tbody = document.getElementById('employeeTableBody');
            if (!tbody) return;

            tbody.innerHTML = '';

            // Calculate pagination
            calculatePagination();
            const employeesToShow = getPaginatedData();

            employeesToShow.forEach((employee, filteredIndex) => {
                const originalIndex = employees.findIndex(emp => emp.id === employee.id);
                const row = tbody.insertRow();
                const yearsOfService = calculateYearsOfService(employee.hireDate);
                const totalAvailable = employee.annualLeave + employee.carriedOverLeave;
                const remainingTotal = totalAvailable - employee.usedAnnual;
                const remainingSick = employee.sickLeave - employee.usedSick;
                const remainingEmergency = employee.emergencyLeave - employee.usedEmergency;
                const status = getEmployeeStatus(employee);

                let statusClass = 'balance-positive';
                if (remainingTotal <= 0) statusClass = 'balance-negative';
                else if (remainingTotal <= 5) statusClass = 'balance-warning';

                const cells = [
                    { content: employee.name, isText: true },
                    { content: employee.id, isText: true },
                    { content: employee.department, isText: true },
                    { content: new Date(employee.hireDate).toLocaleDateString('ar-SA'), isText: true },
                    { content: yearsOfService, isText: true },
                    { content: employee.annualLeave, isText: true },
                    { content: employee.carriedOverLeave, isText: true, className: 'carried-over-highlight' },
                    { content: totalAvailable, isText: true, className: 'balance-positive' },
                    { content: employee.usedAnnual, isText: true },
                    { content: remainingTotal, isText: true, className: statusClass },
                    { content: remainingSick, isText: true, className: remainingSick <= 0 ? 'balance-negative' : remainingSick <= 2 ? 'balance-warning' : 'balance-positive' },
                    { content: remainingEmergency, isText: true, className: remainingEmergency <= 0 ? 'balance-negative' : 'balance-positive' },
                    { content: status, isText: true, className: statusClass }
                ];

                cells.forEach(cellData => {
                    const cell = row.insertCell();
                    cell.textContent = cellData.content;
                    cell.style.padding = '12px';
                    cell.style.textAlign = 'center';
                    cell.style.borderBottom = '1px solid #e9ecef';
                    if (cellData.className) {
                        if (cellData.className.includes('balance-positive')) cell.style.color = '#27ae60';
                        if (cellData.className.includes('balance-warning')) cell.style.color = '#f39c12';
                        if (cellData.className.includes('balance-negative')) cell.style.color = '#e74c3c';
                        if (cellData.className.includes('carried-over-highlight')) {
                            cell.style.backgroundColor = '#e8f5e8';
                            cell.style.fontWeight = '600';
                        }
                    }
                });

                const actionsCell = row.insertCell();
                actionsCell.style.padding = '12px';
                actionsCell.style.textAlign = 'center';
                actionsCell.style.borderBottom = '1px solid #e9ecef';
                actionsCell.innerHTML = `
                    <div class="btn-group">
                        <button class="btn btn-small btn-secondary" onclick="editEmployee(${originalIndex})" title="تعديل البيانات">✏️</button>
                        <button class="btn btn-small btn-success" onclick="openLeaveManagement(${originalIndex})" title="إدارة الإجازات">📅</button>
                        <button class="btn btn-small btn-danger" onclick="deleteEmployee(${originalIndex})" title="حذف الموظف">🗑️</button>
                    </div>
                `;
            });

            // Add pagination controls
            const paginationContainer = document.getElementById('paginationControls');
            if (paginationContainer) {
                paginationContainer.innerHTML = createPaginationControls();
            }
        }

        function updateStats() {
            try {
                console.log('📊 تحديث الإحصائيات...');

                // Ensure employees array exists
                if (!employees || !Array.isArray(employees)) {
                    employees = [];
                }

                const totalEmployees = employees.length;
                const totalCarriedOver = employees.reduce((sum, emp) => sum + (emp.carriedOverLeave || 0), 0);
                const totalUsedLeaves = employees.reduce((sum, emp) => sum + (emp.usedAnnual || 0) + (emp.usedSick || 0) + (emp.usedEmergency || 0), 0);
                const totalRemainingLeaves = employees.reduce((sum, emp) => {
                    const annualRemaining = ((emp.annualLeave || 0) + (emp.carriedOverLeave || 0)) - (emp.usedAnnual || 0);
                    const sickRemaining = (emp.sickLeave || 0) - (emp.usedSick || 0);
                    const emergencyRemaining = (emp.emergencyLeave || 0) - (emp.usedEmergency || 0);
                    return sum + annualRemaining + sickRemaining + emergencyRemaining;
                }, 0);
                const totalAllowedLeaves = employees.reduce((sum, emp) =>
                    sum + (emp.annualLeave || 0) + (emp.carriedOverLeave || 0) + (emp.sickLeave || 0) + (emp.emergencyLeave || 0), 0);
                const averageUsage = totalAllowedLeaves > 0 ? Math.round((totalUsedLeaves / totalAllowedLeaves) * 100) : 0;

                // Update elements with fallback
                const updateElement = (id, value) => {
                    const element = document.getElementById(id);
                    if (element) {
                        element.textContent = value;
                        console.log(`✅ تم تحديث ${id}: ${value}`);
                    } else {
                        console.warn(`⚠️ لم يتم العثور على العنصر: ${id}`);
                    }
                };

                updateElement('totalEmployees', totalEmployees);
                updateElement('totalCarriedOver', totalCarriedOver);
                updateElement('totalUsedLeaves', totalUsedLeaves);
                updateElement('totalRemainingLeaves', totalRemainingLeaves);
                updateElement('averageUsage', averageUsage + '%');

                console.log('✅ تم تحديث الإحصائيات بنجاح');

            } catch (error) {
                console.error('❌ خطأ في تحديث الإحصائيات:', error);

                // Fallback values
                const fallbackUpdate = (id, value) => {
                    const element = document.getElementById(id);
                    if (element) element.textContent = value;
                };

                fallbackUpdate('totalEmployees', '0');
                fallbackUpdate('totalCarriedOver', '0');
                fallbackUpdate('totalUsedLeaves', '0');
                fallbackUpdate('totalRemainingLeaves', '0');
                fallbackUpdate('averageUsage', '0%');
            }
        }

        function updateQuickEmployeeView() {
            const quickView = document.getElementById('quickEmployeeView');
            if (!quickView) return;

            if (employees.length === 0) {
                quickView.innerHTML = `
                    <div style="text-align: center; padding: 40px;">
                        <div style="font-size: 48px; margin-bottom: 20px; opacity: 0.5;">👥</div>
                        <h4 style="color: #2c3e50; margin-bottom: 15px;">مرحباً بك في نظام إدارة الإجازات!</h4>
                        <p style="color: #7f8c8d; margin-bottom: 20px; line-height: 1.6;">
                            لا توجد بيانات موظفين حالياً. ابدأ ببناء قاعدة بيانات الموظفين الخاصة بك.
                        </p>
                        <div style="display: flex; gap: 10px; justify-content: center; flex-wrap: wrap;">
                            <button class="btn btn-primary" onclick="showSection('employees')">
                                ➕ إضافة موظف جديد
                            </button>
                            <button class="btn btn-secondary" onclick="loadTestData()">
                                🧪 تحميل بيانات تجريبية
                            </button>
                        </div>
                    </div>
                `;
                return;
            }

            // Show recent employees with enhanced display
            const recentEmployees = employees.slice(-5);
            const totalEmployees = employees.length;

            quickView.innerHTML = `
                <div style="margin-bottom: 20px; padding: 15px; background: linear-gradient(135deg, #667eea, #764ba2); color: white; border-radius: 8px;">
                    <h4 style="margin: 0 0 10px 0;">📊 ملخص سريع</h4>
                    <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(120px, 1fr)); gap: 15px; text-align: center;">
                        <div>
                            <div style="font-size: 24px; font-weight: bold;">${totalEmployees}</div>
                            <div style="font-size: 12px; opacity: 0.9;">إجمالي الموظفين</div>
                        </div>
                        <div>
                            <div style="font-size: 24px; font-weight: bold;">${employees.filter(emp => getEmployeeStatus(emp) === 'طبيعي').length}</div>
                            <div style="font-size: 12px; opacity: 0.9;">رصيد طبيعي</div>
                        </div>
                        <div>
                            <div style="font-size: 24px; font-weight: bold;">${employees.filter(emp => getEmployeeStatus(emp) === 'رصيد منخفض').length}</div>
                            <div style="font-size: 12px; opacity: 0.9;">رصيد منخفض</div>
                        </div>
                        <div>
                            <div style="font-size: 24px; font-weight: bold;">${employees.filter(emp => getEmployeeStatus(emp) === 'نفد الرصيد').length}</div>
                            <div style="font-size: 12px; opacity: 0.9;">نفد الرصيد</div>
                        </div>
                    </div>
                </div>

                <h5 style="margin-bottom: 15px; color: #2c3e50;">آخر الموظفين المضافين:</h5>
                <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 15px;">
                    ${recentEmployees.map(emp => {
                        const status = getEmployeeStatus(emp);
                        const statusColor = status === 'طبيعي' ? '#27ae60' : status === 'رصيد منخفض' ? '#f39c12' : '#e74c3c';
                        const remaining = (emp.annualLeave + emp.carriedOverLeave) - emp.usedAnnual;
                        return `
                            <div style="padding: 15px; border: 1px solid #e9ecef; border-radius: 8px; background: white; transition: transform 0.2s; cursor: pointer;"
                                 onclick="openLeaveManagement(${employees.findIndex(e => e.id === emp.id)})"
                                 onmouseover="this.style.transform='translateY(-2px)'; this.style.boxShadow='0 4px 15px rgba(0,0,0,0.1)'"
                                 onmouseout="this.style.transform='translateY(0)'; this.style.boxShadow='none'">
                                <h4 style="margin: 0 0 10px 0; color: #2c3e50; font-size: 14px;">${emp.name}</h4>
                                <p style="margin: 5px 0; font-size: 12px; color: #7f8c8d;">🏢 ${emp.department}</p>
                                <p style="margin: 5px 0; font-size: 12px; color: ${statusColor}; font-weight: 600;">📊 ${status}</p>
                                <p style="margin: 5px 0; font-size: 12px; color: #2c3e50;">📅 المتبقي: ${remaining} يوم</p>
                                <div style="margin-top: 10px; font-size: 11px; color: #95a5a6;">انقر لإدارة الإجازات</div>
                            </div>
                        `;
                    }).join('')}
                </div>

                ${totalEmployees > 5 ? `
                    <div style="text-align: center; margin-top: 15px;">
                        <button class="btn btn-primary btn-small" onclick="showSection('employees')">
                            عرض جميع الموظفين (${totalEmployees})
                        </button>
                    </div>
                ` : ''}
            `;
        }

        // Search and filter functions
        const debouncedFilterEmployees = debounce(filterEmployees, 300);

        function filterEmployees() {
            const searchInput = document.getElementById('searchInput');
            const statusFilter = document.getElementById('statusFilter');
            const departmentFilter = document.getElementById('departmentFilter');

            if (!searchInput || !statusFilter || !departmentFilter) return;

            const searchTerm = searchInput.value.toLowerCase().trim();
            const statusValue = statusFilter.value;
            const departmentValue = departmentFilter.value;

            // Advanced search filters
            const hireDateFrom = document.getElementById('hireDateFrom')?.value;
            const hireDateTo = document.getElementById('hireDateTo')?.value;
            const serviceYearsFrom = document.getElementById('serviceYearsFrom')?.value;
            const serviceYearsTo = document.getElementById('serviceYearsTo')?.value;
            const remainingFrom = document.getElementById('remainingFrom')?.value;
            const remainingTo = document.getElementById('remainingTo')?.value;

            filteredEmployees = employees.filter(employee => {
                const matchesSearch = !searchTerm ||
                    employee.name.toLowerCase().includes(searchTerm) ||
                    employee.id.toLowerCase().includes(searchTerm) ||
                    employee.department.toLowerCase().includes(searchTerm);

                const employeeStatus = getEmployeeStatus(employee);
                const matchesStatus = !statusValue || employeeStatus === statusValue;

                const matchesDepartment = !departmentValue || employee.department === departmentValue;

                // Advanced search filters
                const hireDate = new Date(employee.hireDate);
                const matchesHireDateFrom = !hireDateFrom || hireDate >= new Date(hireDateFrom);
                const matchesHireDateTo = !hireDateTo || hireDate <= new Date(hireDateTo);

                const yearsOfService = calculateYearsOfService(employee.hireDate);
                const matchesServiceYearsFrom = !serviceYearsFrom || yearsOfService >= parseInt(serviceYearsFrom);
                const matchesServiceYearsTo = !serviceYearsTo || yearsOfService <= parseInt(serviceYearsTo);

                const totalAvailable = employee.annualLeave + employee.carriedOverLeave;
                const remaining = totalAvailable - employee.usedAnnual;
                const matchesRemainingFrom = !remainingFrom || remaining >= parseInt(remainingFrom);
                const matchesRemainingTo = !remainingTo || remaining <= parseInt(remainingTo);

                return matchesSearch && matchesStatus && matchesDepartment &&
                       matchesHireDateFrom && matchesHireDateTo &&
                       matchesServiceYearsFrom && matchesServiceYearsTo &&
                       matchesRemainingFrom && matchesRemainingTo;
            });

            // Reset to first page when filtering
            currentPage = 1;
            updateTable();
            updateFilterResults();
        }

        function updateFilterResults() {
            const resultsElement = document.getElementById('filterResults');
            if (!resultsElement) return;

            const totalEmployees = employees.length;
            const filteredCount = filteredEmployees.length;

            if (filteredCount === totalEmployees) {
                resultsElement.textContent = `عرض جميع الموظفين (${totalEmployees})`;
            } else {
                resultsElement.textContent = `عرض ${filteredCount} من أصل ${totalEmployees} موظف`;
            }
        }

        function clearFilters() {
            const searchInput = document.getElementById('searchInput');
            const statusFilter = document.getElementById('statusFilter');
            const departmentFilter = document.getElementById('departmentFilter');

            if (searchInput) searchInput.value = '';
            if (statusFilter) statusFilter.value = '';
            if (departmentFilter) departmentFilter.value = '';

            filteredEmployees = [...employees];
            updateTable();
            updateFilterResults();
        }

        function updateDepartmentFilter() {
            const departmentFilter = document.getElementById('departmentFilter');
            if (!departmentFilter) return;

            const currentValue = departmentFilter.value;
            const departments = [...new Set(employees.map(emp => emp.department))].sort();

            departmentFilter.innerHTML = '<option value="">جميع الأقسام</option>';

            departments.forEach(dept => {
                const option = document.createElement('option');
                option.value = dept;
                option.textContent = dept;
                departmentFilter.appendChild(option);
            });

            if (departments.includes(currentValue)) {
                departmentFilter.value = currentValue;
            }
        }

        function hasActiveFilters() {
            const searchInput = document.getElementById('searchInput');
            const statusFilter = document.getElementById('statusFilter');
            const departmentFilter = document.getElementById('departmentFilter');

            return (searchInput && searchInput.value.trim() !== '') ||
                   (statusFilter && statusFilter.value !== '') ||
                   (departmentFilter && departmentFilter.value !== '');
        }

        // Leave management functions
        function openLeaveManagement(index) {
            currentEmployeeIndex = index;
            showSection('leaves');
            setTimeout(() => {
                updateLeaveEmployeeSelect();
                document.getElementById('leaveEmployeeSelect').value = index;
                selectEmployeeForLeave();
            }, 300);
        }

        function updateLeaveEmployeeSelect() {
            const select = document.getElementById('leaveEmployeeSelect');
            if (!select) return;

            select.innerHTML = '<option value="">-- اختر موظف --</option>';
            employees.forEach((emp, index) => {
                const option = document.createElement('option');
                option.value = index;
                option.textContent = `${emp.name} (${emp.id}) - ${emp.department}`;
                select.appendChild(option);
            });
        }

        function selectEmployeeForLeave() {
            const select = document.getElementById('leaveEmployeeSelect');
            const selectedIndex = parseInt(select.value);

            if (isNaN(selectedIndex) || selectedIndex < 0 || selectedIndex >= employees.length) {
                document.getElementById('selectedEmployeeInfo').style.display = 'none';
                document.getElementById('leaveManagementSection').style.display = 'none';
                return;
            }

            currentEmployeeIndex = selectedIndex;
            const employee = employees[selectedIndex];

            updateSelectedEmployeeInfo(employee);
            updateLeaveHistory();

            document.getElementById('selectedEmployeeInfo').style.display = 'block';
            document.getElementById('leaveManagementSection').style.display = 'block';
        }

        function updateSelectedEmployeeInfo(employee) {
            const infoDiv = document.getElementById('selectedEmployeeInfo');
            const totalAvailable = employee.annualLeave + employee.carriedOverLeave;
            const remainingTotal = totalAvailable - employee.usedAnnual;

            infoDiv.innerHTML = `
                <h4 style="margin: 0 0 15px 0; color: #2c3e50;">📊 معلومات الرصيد - ${employee.name}</h4>
                <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 15px;">
                    <div>
                        <strong>الرصيد السنوي:</strong> ${employee.annualLeave} يوم<br>
                        <strong>الرصيد المرحل:</strong> <span style="color: #27ae60; font-weight: 600;">${employee.carriedOverLeave} يوم</span><br>
                        <strong>إجمالي المتاح:</strong> <strong>${totalAvailable} يوم</strong>
                    </div>
                    <div>
                        <strong>المستخدم:</strong> ${employee.usedAnnual} يوم<br>
                        <strong>المتبقي:</strong> <strong style="color: ${remainingTotal <= 5 ? '#e74c3c' : '#27ae60'}">${remainingTotal} يوم</strong><br>
                        <strong>الحالة:</strong> <span style="color: ${getEmployeeStatus(employee) === 'طبيعي' ? '#27ae60' : '#e74c3c'}">${getEmployeeStatus(employee)}</span>
                    </div>
                </div>
            `;
        }

        function calculateLeaveDays() {
            const startDate = document.getElementById('leaveStartDate').value;
            const endDate = document.getElementById('leaveEndDate').value;

            if (startDate && endDate) {
                const start = new Date(startDate);
                const end = new Date(endDate);
                const diffTime = Math.abs(end - start);
                const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24)) + 1;

                document.getElementById('leaveDays').value = diffDays;
            }
        }

        function addLeaveRecord() {
            if (currentEmployeeIndex === -1) return;

            const employee = employees[currentEmployeeIndex];
            const leaveType = sanitizeInput(document.getElementById('leaveType').value);
            const leaveDays = parseFloat(document.getElementById('leaveDays').value);
            const startDate = document.getElementById('leaveStartDate').value;
            const endDate = document.getElementById('leaveEndDate').value;
            const reason = sanitizeInput(document.getElementById('leaveReason').value.trim());

            if (!leaveType || !leaveDays || !startDate || !endDate || !reason) {
                showAlert('يرجى ملء جميع الحقول المطلوبة', 'warning');
                return;
            }

            if (leaveDays <= 0) {
                showAlert('عدد أيام الإجازة يجب أن يكون أكبر من صفر', 'warning');
                return;
            }

            let currentUsed, maxAllowed, leaveTypeName;
            switch(leaveType) {
                case 'annual':
                    currentUsed = employee.usedAnnual;
                    maxAllowed = employee.annualLeave + employee.carriedOverLeave;
                    leaveTypeName = 'إجازة سنوية';
                    break;
                case 'sick':
                    currentUsed = employee.usedSick;
                    maxAllowed = employee.sickLeave;
                    leaveTypeName = 'إجازة مرضية';
                    break;
                case 'emergency':
                    currentUsed = employee.usedEmergency;
                    maxAllowed = employee.emergencyLeave;
                    leaveTypeName = 'إجازة طارئة';
                    break;
            }

            if (currentUsed + leaveDays > maxAllowed) {
                showAlert(`الرصيد المتاح لـ${leaveTypeName} غير كافي. المتاح: ${maxAllowed - currentUsed} يوم`, 'warning');
                return;
            }

            const leaveRecord = {
                id: Date.now(),
                type: leaveType,
                typeName: leaveTypeName,
                days: leaveDays,
                startDate: startDate,
                endDate: endDate,
                reason: reason,
                addedDate: new Date().toISOString()
            };

            if (!employee.leaveHistory) {
                employee.leaveHistory = [];
            }
            employee.leaveHistory.push(leaveRecord);

            switch(leaveType) {
                case 'annual':
                    employee.usedAnnual += leaveDays;
                    break;
                case 'sick':
                    employee.usedSick += leaveDays;
                    break;
                case 'emergency':
                    employee.usedEmergency += leaveDays;
                    break;
            }

            updateTable();
            updateStats();
            updateSelectedEmployeeInfo(employee);
            updateLeaveHistory();
            saveData();

            showAlert(`تم إضافة ${leaveTypeName} لمدة ${leaveDays} يوم للموظف ${employee.name}`, 'success');

            // Clear form
            document.getElementById('leaveDays').value = '';
            document.getElementById('leaveStartDate').value = '';
            document.getElementById('leaveEndDate').value = '';
            document.getElementById('leaveReason').value = '';
        }

        function updateLeaveHistory() {
            if (currentEmployeeIndex === -1) return;

            const employee = employees[currentEmployeeIndex];
            const historyContent = document.getElementById('leaveHistoryContent');

            if (!employee.leaveHistory || employee.leaveHistory.length === 0) {
                historyContent.innerHTML = '<p style="text-align: center; color: #7f8c8d; padding: 40px;">لا توجد إجازات مسجلة لهذا الموظف</p>';
                return;
            }

            const sortedHistory = [...employee.leaveHistory].sort((a, b) => new Date(b.addedDate) - new Date(a.addedDate));

            historyContent.innerHTML = sortedHistory.map(record => `
                <div style="padding: 15px; margin: 10px 0; border: 1px solid #e9ecef; border-radius: 8px; background: white;">
                    <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 10px;">
                        <strong style="color: #2c3e50;">${record.typeName}</strong>
                        <button class="btn btn-small btn-danger" onclick="deleteLeaveRecord(${record.id})" title="حذف الإجازة">🗑️</button>
                    </div>
                    <div style="font-size: 14px; color: #7f8c8d;">
                        📅 من ${new Date(record.startDate).toLocaleDateString('ar-SA')} إلى ${new Date(record.endDate).toLocaleDateString('ar-SA')}<br>
                        ⏱️ المدة: ${record.days} يوم<br>
                        📝 السبب: ${record.reason}<br>
                        <small style="color: #95a5a6;">تم الإضافة: ${new Date(record.addedDate).toLocaleDateString('ar-SA')}</small>
                    </div>
                </div>
            `).join('');
        }

        function deleteLeaveRecord(recordId) {
            if (currentEmployeeIndex === -1) return;

            const employee = employees[currentEmployeeIndex];
            const recordIndex = employee.leaveHistory.findIndex(record => record.id === recordId);

            if (recordIndex === -1) return;

            const record = employee.leaveHistory[recordIndex];

            if (confirm(`هل أنت متأكد من حذف إجازة ${record.typeName} لمدة ${record.days} يوم؟`)) {
                switch(record.type) {
                    case 'annual':
                        employee.usedAnnual -= record.days;
                        break;
                    case 'sick':
                        employee.usedSick -= record.days;
                        break;
                    case 'emergency':
                        employee.usedEmergency -= record.days;
                        break;
                }

                employee.leaveHistory.splice(recordIndex, 1);

                updateTable();
                updateStats();
                updateSelectedEmployeeInfo(employee);
                updateLeaveHistory();
                saveData();

                showAlert(`تم حذف إجازة ${record.typeName} بنجاح`, 'success');
            }
        }

        // Export and report functions
        function exportToCSV() {
            if (employees.length === 0) {
                showAlert('لا توجد بيانات للتصدير', 'warning');
                return;
            }

            const headers = [
                'اسم الموظف', 'الرقم الوظيفي', 'القسم', 'تاريخ التوظيف', 'سنوات الخدمة',
                'الرصيد السنوي', 'الرصيد المرحل', 'إجمالي المتاح', 'المستخدم السنوي', 'المتبقي السنوي',
                'الرصيد المرضي', 'المستخدم المرضي', 'المتبقي المرضي',
                'الرصيد الطارئ', 'المستخدم الطارئ', 'المتبقي الطارئ', 'الحالة'
            ];

            const csvContent = [
                headers.join(','),
                ...employees.map(emp => {
                    const yearsOfService = calculateYearsOfService(emp.hireDate);
                    const totalAvailable = emp.annualLeave + emp.carriedOverLeave;
                    const remainingTotal = totalAvailable - emp.usedAnnual;
                    const status = getEmployeeStatus(emp);
                    return [
                        emp.name, emp.id, emp.department, emp.hireDate, yearsOfService,
                        emp.annualLeave, emp.carriedOverLeave, totalAvailable, emp.usedAnnual, remainingTotal,
                        emp.sickLeave, emp.usedSick, emp.sickLeave - emp.usedSick,
                        emp.emergencyLeave, emp.usedEmergency, emp.emergencyLeave - emp.usedEmergency,
                        status
                    ].join(',');
                })
            ].join('\n');

            downloadFile(csvContent, 'employee_leaves.csv', 'text/csv;charset=utf-8;');
            showAlert('تم تصدير البيانات بصيغة CSV بنجاح', 'success');
        }

        function exportToJSON() {
            const data = saveData();
            downloadFile(data, `employee_leaves_backup_${new Date().toISOString().split('T')[0]}.json`, 'application/json');
            showAlert('تم إنشاء النسخة الاحتياطية بنجاح', 'success');
        }

        function downloadFile(content, filename, contentType) {
            const blob = new Blob([content], { type: contentType });
            const url = window.URL.createObjectURL(blob);
            const link = document.createElement('a');
            link.href = url;
            link.download = filename;
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);
            window.URL.revokeObjectURL(url);
        }

        function generateDetailedReport() {
            const reportContent = document.getElementById('detailedReportContent');

            if (employees.length === 0) {
                reportContent.innerHTML = '<p style="text-align: center; color: #7f8c8d; padding: 40px;">لا توجد بيانات لإنشاء التقرير</p>';
                return;
            }

            const departmentStats = {};
            employees.forEach(emp => {
                if (!departmentStats[emp.department]) {
                    departmentStats[emp.department] = {
                        count: 0,
                        totalCarriedOver: 0,
                        totalUsed: 0,
                        totalRemaining: 0
                    };
                }
                departmentStats[emp.department].count++;
                departmentStats[emp.department].totalCarriedOver += emp.carriedOverLeave;
                departmentStats[emp.department].totalUsed += emp.usedAnnual + emp.usedSick + emp.usedEmergency;
                departmentStats[emp.department].totalRemaining += (emp.annualLeave + emp.carriedOverLeave - emp.usedAnnual) + (emp.sickLeave - emp.usedSick) + (emp.emergencyLeave - emp.usedEmergency);
            });

            reportContent.innerHTML = `
                <div class="card">
                    <div class="card-header">
                        <h3 class="card-title">📊 تقرير مفصل حسب الأقسام</h3>
                    </div>
                    <div class="card-body">
                        <div style="overflow-x: auto;">
                            <table style="width: 100%; border-collapse: collapse;">
                                <thead>
                                    <tr style="background: #f8f9fa;">
                                        <th style="padding: 12px; border: 1px solid #dee2e6;">القسم</th>
                                        <th style="padding: 12px; border: 1px solid #dee2e6;">عدد الموظفين</th>
                                        <th style="padding: 12px; border: 1px solid #dee2e6;">إجمالي الرصيد المرحل</th>
                                        <th style="padding: 12px; border: 1px solid #dee2e6;">إجمالي المستخدم</th>
                                        <th style="padding: 12px; border: 1px solid #dee2e6;">إجمالي المتبقي</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    ${Object.entries(departmentStats).map(([dept, stats]) => `
                                        <tr>
                                            <td style="padding: 12px; border: 1px solid #dee2e6;">${dept}</td>
                                            <td style="padding: 12px; border: 1px solid #dee2e6; text-align: center;">${stats.count}</td>
                                            <td style="padding: 12px; border: 1px solid #dee2e6; text-align: center;">${stats.totalCarriedOver}</td>
                                            <td style="padding: 12px; border: 1px solid #dee2e6; text-align: center;">${stats.totalUsed}</td>
                                            <td style="padding: 12px; border: 1px solid #dee2e6; text-align: center;">${stats.totalRemaining}</td>
                                        </tr>
                                    `).join('')}
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            `;
        }

        function importData() {
            const fileInput = document.getElementById('importFile');
            const file = fileInput.files[0];

            if (!file) return;

            const reader = new FileReader();
            reader.onload = function(e) {
                try {
                    const data = JSON.parse(e.target.result);

                    if (data.employees && Array.isArray(data.employees)) {
                        if (confirm('هل أنت متأكد من استيراد البيانات؟ سيتم استبدال البيانات الحالية.')) {
                            employees = data.employees;
                            filteredEmployees = [...employees];
                            updateTable();
                            updateStats();
                            updateQuickEmployeeView();
                            updateDepartmentFilter();
                            saveData();
                            showAlert(`تم استيراد ${employees.length} موظف بنجاح`, 'success');
                        }
                    } else {
                        showAlert('ملف غير صالح. يرجى التأكد من صحة الملف', 'danger');
                    }
                } catch (error) {
                    showAlert('خطأ في قراءة الملف. يرجى التأكد من صحة الملف', 'danger');
                }
            };
            reader.readAsText(file);

            fileInput.value = '';
        }

        function resetData() {
            if (confirm('هل أنت متأكد من إعادة تعيين جميع الإجازات المستخدمة؟\nسيتم الاحتفاظ ببيانات الموظفين ولكن سيتم مسح سجل الإجازات.')) {
                employees.forEach(emp => {
                    emp.usedAnnual = 0;
                    emp.usedSick = 0;
                    emp.usedEmergency = 0;
                    emp.leaveHistory = [];
                });
                updateTable();
                updateStats();
                updateQuickEmployeeView();
                saveData();
                showAlert('تم إعادة تعيين البيانات بنجاح', 'success');
            }
        }

        function clearAllData() {
            if (confirm('هل أنت متأكد من حذف جميع البيانات؟\nسيتم حذف البيانات من التخزين المحلي أيضاً.\nهذا الإجراء لا يمكن التراجع عنه.')) {
                if (confirm('تأكيد أخير: سيتم حذف جميع بيانات الموظفين والإجازات نهائياً من الذاكرة والتخزين المحلي!')) {
                    employees = [];
                    filteredEmployees = [];
                    localStorage.removeItem('employeeLeaveSystem');
                    updateTable();
                    updateStats();
                    updateQuickEmployeeView();
                    showAlert('تم مسح جميع البيانات من الذاكرة والتخزين المحلي', 'success');
                }
            }
        }

        // Test data functions (simplified version)
        function loadTestData() {
            if (confirm('هل أنت متأكد من تحميل البيانات التجريبية؟\nسيتم استبدال البيانات الحالية بـ 5 موظفين تجريبيين.')) {
                employees = [
                    {
                        name: 'أحمد محمد العلي',
                        id: 'EMP001',
                        department: 'تقنية المعلومات',
                        hireDate: '2020-01-15',
                        annualLeave: 30,
                        carriedOverLeave: 12,
                        sickLeave: 15,
                        emergencyLeave: 5,
                        usedAnnual: 8,
                        usedSick: 2,
                        usedEmergency: 0,
                        leaveHistory: []
                    },
                    {
                        name: 'Sarah Johnson',
                        id: 'EMP002',
                        department: 'الموارد البشرية',
                        hireDate: '2019-03-20',
                        annualLeave: 35,
                        carriedOverLeave: 8,
                        sickLeave: 20,
                        emergencyLeave: 7,
                        usedAnnual: 25,
                        usedSick: 5,
                        usedEmergency: 2,
                        leaveHistory: []
                    },
                    {
                        name: 'فاطمة أحمد الزهراني',
                        id: 'EMP003',
                        department: 'المالية',
                        hireDate: '2021-06-10',
                        annualLeave: 25,
                        carriedOverLeave: 5,
                        sickLeave: 12,
                        emergencyLeave: 4,
                        usedAnnual: 28,
                        usedSick: 8,
                        usedEmergency: 1,
                        leaveHistory: []
                    },
                    {
                        name: 'محمد عبدالله الشمري',
                        id: 'EMP004',
                        department: 'التسويق',
                        hireDate: '2022-09-01',
                        annualLeave: 28,
                        carriedOverLeave: 0,
                        sickLeave: 10,
                        emergencyLeave: 3,
                        usedAnnual: 15,
                        usedSick: 0,
                        usedEmergency: 0,
                        leaveHistory: []
                    },
                    {
                        name: 'نورا خالد المطيري',
                        id: 'EMP005',
                        department: 'العمليات',
                        hireDate: '2017-05-08',
                        annualLeave: 38,
                        carriedOverLeave: 20,
                        sickLeave: 18,
                        emergencyLeave: 6,
                        usedAnnual: 45,
                        usedSick: 3,
                        usedEmergency: 1,
                        leaveHistory: []
                    }
                ];

                filteredEmployees = [...employees];
                updateDepartmentFilter();
                updateTable();
                updateStats();
                updateQuickEmployeeView();
                updateCharts();
                saveData();

                showAlert(`تم تحميل ${employees.length} موظف تجريبي بنجاح`, 'success');

                // If we're on dashboard, refresh it
                if (currentSection === 'dashboard') {
                    setTimeout(() => {
                        updateStats();
                        updateQuickEmployeeView();
                        updateCharts();
                    }, 500);
                }
            }
        }

        function runComprehensiveTest() {
            showAlert('🧪 بدء الاختبار الشامل للنظام...', 'info');

            setTimeout(() => {
                const stats = {
                    totalEmployees: employees.length,
                    totalCarriedOver: employees.reduce((sum, emp) => sum + (emp.carriedOverLeave || 0), 0),
                    totalUsed: employees.reduce((sum, emp) => sum + emp.usedAnnual + emp.usedSick + emp.usedEmergency, 0)
                };

                console.log('📊 إحصائيات النظام:', stats);
                showAlert(`✅ تم إكمال الاختبار! الموظفين: ${stats.totalEmployees}، الرصيد المرحل: ${stats.totalCarriedOver}، المستخدم: ${stats.totalUsed}`, 'success');
            }, 1000);
        }

        // Clean up any malformed content
        function cleanupPage() {
            // Remove any elements with malformed CSS or content
            const elements = document.querySelectorAll('*');
            elements.forEach(el => {
                if (el.textContent && el.textContent.includes('background-repeat') && el.textContent.includes('background-position')) {
                    el.classList.add('hidden-content');
                }
            });
        }

        // Initialize the application
        document.addEventListener('DOMContentLoaded', function() {
            console.log('🚀 بدء تحميل النظام...');

            try {
                // Clean up page first
                setTimeout(cleanupPage, 100);

                // Load theme preference
                loadTheme();
                console.log('✅ تم تحميل إعدادات الثيم');

                // Initialize empty arrays
                employees = [];
                filteredEmployees = [];

                // Load data first
                loadData();
                console.log('✅ تم تحميل البيانات');

                // Force load dashboard immediately
                setTimeout(() => {
                    console.log('🎯 تحميل لوحة التحكم...');
                    loadDashboard();

                    // Ensure dashboard is visible
                    const dashboardSection = document.getElementById('dashboard');
                    if (dashboardSection) {
                        dashboardSection.classList.add('active');
                        dashboardSection.style.display = 'block';
                    }

                    // Hide other sections
                    ['employees', 'leaves', 'reports', 'settings'].forEach(sectionId => {
                        const section = document.getElementById(sectionId);
                        if (section) {
                            section.classList.remove('active');
                            section.style.display = 'none';
                        }
                    });

                    // Update stats and content
                    updateStats();
                    updateQuickEmployeeView();

                    // Update charts after DOM is ready
                    setTimeout(() => {
                        updateCharts();
                    }, 200);

                    console.log('✅ تم تحميل لوحة التحكم بنجاح');
                }, 200);

                // Check leave balances after loading data
                setTimeout(() => {
                    if (employees.length > 0) {
                        checkLeaveBalances();
                    }
                }, 2000);

                // Auto-save every 30 seconds
                setInterval(() => {
                    if (employees.length > 0) {
                        saveData();
                    }
                }, 30000);

                // Check leave balances every 5 minutes
                setInterval(() => {
                    if (employees.length > 0) {
                        checkLeaveBalances();
                    }
                }, 300000);

                // Add keyboard shortcuts
                document.addEventListener('keydown', handleKeyboardShortcuts);

                console.log('🎉 تم تحميل النظام بنجاح!');

            } catch (error) {
                console.error('❌ خطأ في تحميل النظام:', error);

                // Fallback initialization
                setTimeout(() => {
                    employees = [];
                    filteredEmployees = [];
                    loadDashboard();
                    showNotification('تم تحميل النظام مع إعدادات افتراضية', 'warning', 5000);
                }, 500);
            }
        });

        // Initialize dashboard properly
        function initializeDashboard() {
            // Ensure dashboard section is active and visible
            const dashboardSection = document.getElementById('dashboard');
            if (dashboardSection) {
                dashboardSection.classList.add('active');
                dashboardSection.style.display = 'block';
            }

            // Hide other sections
            ['employees', 'leaves', 'reports', 'settings'].forEach(sectionId => {
                const section = document.getElementById(sectionId);
                if (section) {
                    section.classList.remove('active');
                    section.style.display = 'none';
                }
            });

            // Update navigation
            document.querySelectorAll('.nav-item').forEach(item => {
                item.classList.remove('active');
            });
            const dashboardNavItem = document.querySelector('.nav-item[onclick*="dashboard"]');
            if (dashboardNavItem) {
                dashboardNavItem.classList.add('active');
            }

            // Force update all dashboard components
            updateStats();
            updateQuickEmployeeView();

            // Update charts after a brief delay to ensure canvas elements are ready
            setTimeout(() => {
                updateCharts();
            }, 500);

            // Show welcome message for new users
            if (employees.length === 0) {
                setTimeout(() => {
                    showNotification('مرحباً بك في نظام إدارة الإجازات! ابدأ بإضافة موظفين من قسم "إدارة الموظفين"', 'info', 8000);
                }, 1500);
            } else {
                setTimeout(() => {
                    showNotification(`مرحباً بك! لديك ${employees.length} موظف في النظام`, 'info', 4000);
                }, 1500);
            }
        }

        // Keyboard shortcuts handler
        function handleKeyboardShortcuts(event) {
            if (event.ctrlKey || event.metaKey) {
                switch(event.key) {
                    case '1':
                        event.preventDefault();
                        showSection('dashboard');
                        break;
                    case '2':
                        event.preventDefault();
                        showSection('employees');
                        break;
                    case '3':
                        event.preventDefault();
                        showSection('leaves');
                        break;
                    case '4':
                        event.preventDefault();
                        showSection('reports');
                        break;
                    case '5':
                        event.preventDefault();
                        showSection('settings');
                        break;
                    case 's':
                        event.preventDefault();
                        saveData();
                        showNotification('تم الحفظ بنجاح', 'info', 2000);
                        break;
                    case 'f':
                        event.preventDefault();
                        const searchInput = document.getElementById('searchInput');
                        if (searchInput) {
                            searchInput.focus();
                            searchInput.select();
                        }
                        break;
                    case 'n':
                        event.preventDefault();
                        if (currentSection === 'employees') {
                            document.getElementById('employeeName')?.focus();
                        }
                        break;
                    case 'e':
                        event.preventDefault();
                        if (currentSection === 'reports') {
                            exportToCSV();
                        }
                        break;
                    case 't':
                        event.preventDefault();
                        toggleTheme();
                        break;
                    case 'h':
                        event.preventDefault();
                        showSection('settings');
                        setTimeout(() => startUserGuide(), 300);
                        break;
                    case '/':
                        event.preventDefault();
                        showSection('settings');
                        setTimeout(() => showKeyboardShortcuts(), 300);
                        break;
                }
            } else if (event.key === 'F1') {
                event.preventDefault();
                showSection('settings');
                setTimeout(() => showTips(), 300);
            } else if (event.key === 'Escape') {
                // Close any open modals or advanced search
                const advancedSearch = document.getElementById('advancedSearch');
                if (advancedSearch && advancedSearch.style.display !== 'none') {
                    toggleAdvancedSearch();
                }
            }
        }
    </script>
</body>
</html>
