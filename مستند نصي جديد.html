<!DOCTYPE html>
<html dir="rtl" lang="ar">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>نظام حساب إجازات الموظفين المتطور</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        
        .container {
            max-width: 1600px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        
        .header {
            background: linear-gradient(135deg, #2c3e50, #3498db);
            color: white;
            padding: 30px;
            text-align: center;
            position: relative;
        }
        
        .header h1 {
            margin: 0;
            font-size: 2.5em;
            font-weight: 300;
        }
        
        .save-indicator {
            position: absolute;
            top: 20px;
            left: 20px;
            background: rgba(255,255,255,0.2);
            padding: 8px 15px;
            border-radius: 20px;
            font-size: 14px;
        }
        
        .save-indicator.saving {
            background: rgba(255,193,7,0.8);
            color: #000;
        }
        
        .save-indicator.saved {
            background: rgba(40,167,69,0.8);
            color: #fff;
        }
        
        .controls {
            padding: 30px;
            background: #f8f9fa;
            border-bottom: 2px solid #e9ecef;
        }
        
        .form-row {
            display: flex;
            gap: 20px;
            margin-bottom: 20px;
            flex-wrap: wrap;
            align-items: end;
        }
        
        .form-group {
            flex: 1;
            min-width: 200px;
        }
        
        .form-group label {
            display: block;
            margin-bottom: 8px;
            font-weight: 600;
            color: #2c3e50;
        }
        
        .form-group input, .form-group select {
            width: 100%;
            padding: 12px;
            border: 2px solid #e9ecef;
            border-radius: 8px;
            font-size: 14px;
            transition: border-color 0.3s;
        }
        
        .form-group input:focus, .form-group select:focus {
            outline: none;
            border-color: #3498db;
        }
        
        .btn {
            background: linear-gradient(135deg, #3498db, #2980b9);
            color: white;
            border: none;
            padding: 12px 25px;
            border-radius: 8px;
            cursor: pointer;
            font-size: 16px;
            font-weight: 600;
            transition: transform 0.2s;
            margin: 5px;
        }
        
        .btn:hover {
            transform: translateY(-2px);
        }
        
        .btn-danger {
            background: linear-gradient(135deg, #e74c3c, #c0392b);
        }
        
        .btn-success {
            background: linear-gradient(135deg, #27ae60, #229954);
        }
        
        .btn-warning {
            background: linear-gradient(135deg, #f39c12, #e67e22);
        }
        
        .btn-small {
            padding: 6px 12px;
            font-size: 12px;
        }
        
        .table-container {
            padding: 30px;
            overflow-x: auto;
        }
        
        table {
            width: 100%;
            border-collapse: collapse;
            background: white;
            border-radius: 10px;
            overflow: hidden;
            box-shadow: 0 5px 15px rgba(0,0,0,0.08);
        }
        
        th {
            background: linear-gradient(135deg, #34495e, #2c3e50);
            color: white;
            padding: 15px;
            font-weight: 600;
            text-align: center;
        }
        
        td {
            padding: 12px 15px;
            border-bottom: 1px solid #ecf0f1;
            text-align: center;
        }
        
        tr:hover {
            background: #f8f9fa;
        }
        
        .leave-balance {
            font-weight: bold;
        }
        
        .balance-positive {
            color: #27ae60;
        }
        
        .balance-negative {
            color: #e74c3c;
        }
        
        .balance-warning {
            color: #f39c12;
        }
        
        .stats {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            padding: 30px;
            background: #f8f9fa;
        }
        
        .stat-card {
            background: white;
            padding: 25px;
            border-radius: 10px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.08);
            text-align: center;
        }
        
        .stat-card h3 {
            margin: 0 0 10px 0;
            color: #2c3e50;
        }
        
        .stat-card .number {
            font-size: 2em;
            font-weight: bold;
            color: #3498db;
        }
        
        .export-section {
            padding: 30px;
            text-align: center;
            background: #ecf0f1;
        }
        
        .alert {
            padding: 15px;
            margin: 20px 0;
            border-radius: 8px;
            font-weight: 600;
        }
        
        .alert-success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        
        .alert-warning {
            background: #fff3cd;
            color: #856404;
            border: 1px solid #ffeaa7;
        }
        
        .alert-danger {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        
        /* Modal Styles */
        .modal {
            display: none;
            position: fixed;
            z-index: 1000;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0,0,0,0.5);
        }
        
        .modal-content {
            background-color: #fefefe;
            margin: 10% auto;
            padding: 30px;
            border-radius: 15px;
            width: 90%;
            max-width: 600px;
            box-shadow: 0 20px 60px rgba(0,0,0,0.3);
        }
        
        .close {
            color: #aaa;
            float: left;
            font-size: 28px;
            font-weight: bold;
            cursor: pointer;
        }
        
        .close:hover {
            color: #000;
        }
        
        .modal h2 {
            color: #2c3e50;
            margin-bottom: 25px;
            text-align: center;
        }
        
        .leave-history {
            max-height: 300px;
            overflow-y: auto;
            margin: 20px 0;
            border: 1px solid #e9ecef;
            border-radius: 8px;
        }
        
        .leave-record {
            padding: 15px;
            border-bottom: 1px solid #f1f1f1;
        }
        
        .leave-record:last-child {
            border-bottom: none;
        }
        
        .leave-record-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 5px;
        }
        
        .delete-leave {
            background: #dc3545;
            color: white;
            border: none;
            padding: 4px 8px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 12px;
        }
        
        .backup-controls {
            display: flex;
            gap: 10px;
            justify-content: center;
            margin: 20px 0;
        }
        
        .file-input {
            display: none;
        }
        
        .file-input-label {
            background: linear-gradient(135deg, #17a2b8, #138496);
            color: white;
            padding: 12px 25px;
            border-radius: 8px;
            cursor: pointer;
            font-weight: 600;
            display: inline-block;
        }
        
        .file-input-label:hover {
            transform: translateY(-2px);
        }

        /* Enhanced validation styles */
        .form-group input.invalid {
            border-color: #e74c3c !important;
            box-shadow: 0 0 5px rgba(231, 76, 60, 0.3);
        }

        .form-group input.warning {
            border-color: #f39c12 !important;
            box-shadow: 0 0 5px rgba(243, 156, 18, 0.3);
        }

        .form-group input.valid {
            border-color: #27ae60 !important;
            box-shadow: 0 0 5px rgba(39, 174, 96, 0.3);
        }

        /* Search and filter enhancements */
        #searchInput {
            background-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" viewBox="0 0 16 16"><path d="M11.742 10.344a6.5 6.5 0 1 0-1.397 1.398h-.001c.03.04.062.078.098.115l3.85 3.85a1 1 0 0 0 1.415-1.414l-3.85-3.85a1.007 1.007 0 0 0-.115-.1zM12 6.5a5.5 5.5 0 1 1-11 0 5.5 5.5 0 0 1 11 0z"/></svg>');
            background-repeat: no-repeat;
            background-position: 10px center;
            padding-left: 40px;
        }

        .filter-active {
            background-color: #e3f2fd !important;
            border-color: #2196f3 !important;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <div class="save-indicator" id="saveIndicator">محفوظ تلقائياً</div>
            <h1>🏢 نظام حساب إجازات الموظفين المتطور</h1>
            <p>إدارة احترافية لإجازات الموظفين مع الحفظ التلقائي</p>
        </div>
        
        <div class="controls">
            <div class="form-row">
                <div class="form-group">
                    <label>اسم الموظف *</label>
                    <input type="text" id="employeeName" placeholder="أدخل اسم الموظف" required
                           oninput="validateTextInput(this, /^[\u0600-\u06FFa-zA-Z\s]+$/, 'اسم الموظف يجب أن يحتوي على أحرف عربية أو إنجليزية فقط')"
                           title="أدخل اسم الموظف (أحرف عربية أو إنجليزية فقط)">
                </div>
                <div class="form-group">
                    <label>الرقم الوظيفي *</label>
                    <input type="text" id="employeeId" placeholder="أدخل الرقم الوظيفي" required
                           oninput="validateTextInput(this, /^[a-zA-Z0-9]+$/, 'الرقم الوظيفي يجب أن يحتوي على أرقام وأحرف إنجليزية فقط')"
                           title="أدخل الرقم الوظيفي (أرقام وأحرف إنجليزية فقط)">
                </div>
                <div class="form-group">
                    <label>القسم *</label>
                    <input type="text" id="department" placeholder="أدخل القسم" required
                           oninput="validateTextInput(this, /^[\u0600-\u06FFa-zA-Z\s]+$/, 'اسم القسم يجب أن يحتوي على أحرف عربية أو إنجليزية فقط')"
                           title="أدخل اسم القسم (أحرف عربية أو إنجليزية فقط)">
                </div>
                <div class="form-group">
                    <label>تاريخ التوظيف *</label>
                    <input type="date" id="hireDate" required
                           onchange="validateDateInput(this, false)"
                           title="أدخل تاريخ التوظيف">
                </div>
            </div>
            
            <div class="form-row">
                <div class="form-group">
                    <label>الرصيد السنوي (يوم)</label>
                    <input type="number" id="annualLeave" value="30" min="0" max="365" step="1"
                           oninput="validateNumberInput(this, 0, 365)"
                           title="أدخل رقم بين 0 و 365">
                </div>
                <div class="form-group">
                    <label>إجازة مرضية (يوم)</label>
                    <input type="number" id="sickLeave" value="15" min="0" max="90" step="1"
                           oninput="validateNumberInput(this, 0, 90)"
                           title="أدخل رقم بين 0 و 90">
                </div>
                <div class="form-group">
                    <label>إجازة طارئة (يوم)</label>
                    <input type="number" id="emergencyLeave" value="5" min="0" max="30" step="1"
                           oninput="validateNumberInput(this, 0, 30)"
                           title="أدخل رقم بين 0 و 30">
                </div>
                <div class="form-group">
                    <button class="btn" onclick="addEmployee()" id="addEmployeeBtn">إضافة موظف</button>
                </div>
            </div>
        </div>
        
        <div id="alertContainer"></div>

        <!-- Search and Filter Section -->
        <div class="controls" style="border-bottom: none; padding-bottom: 15px;">
            <div class="form-row">
                <div class="form-group">
                    <label>🔍 البحث في الموظفين</label>
                    <input type="text" id="searchInput" placeholder="ابحث بالاسم، الرقم الوظيفي، أو القسم..."
                           oninput="filterEmployees()" title="ابحث في بيانات الموظفين">
                </div>
                <div class="form-group">
                    <label>📊 فلترة حسب الحالة</label>
                    <select id="statusFilter" onchange="filterEmployees()">
                        <option value="">جميع الحالات</option>
                        <option value="طبيعي">طبيعي</option>
                        <option value="رصيد منخفض">رصيد منخفض</option>
                        <option value="نفد الرصيد">نفد الرصيد</option>
                    </select>
                </div>
                <div class="form-group">
                    <label>🏢 فلترة حسب القسم</label>
                    <select id="departmentFilter" onchange="filterEmployees()">
                        <option value="">جميع الأقسام</option>
                    </select>
                </div>
                <div class="form-group">
                    <button class="btn btn-warning" onclick="clearFilters()" title="مسح جميع الفلاتر">🗑️ مسح الفلاتر</button>
                </div>
            </div>
            <div style="margin-top: 10px; font-size: 14px; color: #666;">
                <span id="filterResults">عرض جميع الموظفين</span>
            </div>
        </div>

        <div class="table-container">
            <table id="employeeTable">
                <thead>
                    <tr>
                        <th>اسم الموظف</th>
                        <th>الرقم الوظيفي</th>
                        <th>القسم</th>
                        <th>تاريخ التوظيف</th>
                        <th>سنوات الخدمة</th>
                        <th>الرصيد السنوي</th>
                        <th>المستخدم</th>
                        <th>المتبقي</th>
                        <th>إجازة مرضية</th>
                        <th>إجازة طارئة</th>
                        <th>الحالة</th>
                        <th>إجراءات</th>
                    </tr>
                </thead>
                <tbody id="employeeTableBody">
                </tbody>
            </table>
        </div>
        
        <div class="stats">
            <div class="stat-card">
                <h3>إجمالي الموظفين</h3>
                <div class="number" id="totalEmployees">0</div>
            </div>
            <div class="stat-card">
                <h3>إجمالي الإجازات المستخدمة</h3>
                <div class="number" id="totalUsedLeaves">0</div>
            </div>
            <div class="stat-card">
                <h3>إجمالي الإجازات المتبقية</h3>
                <div class="number" id="totalRemainingLeaves">0</div>
            </div>
            <div class="stat-card">
                <h3>متوسط الاستخدام</h3>
                <div class="number" id="averageUsage">0%</div>
            </div>
        </div>
        
        <div class="export-section">
            <h3>إدارة البيانات</h3>
            <div class="backup-controls">
                <button class="btn btn-success" onclick="exportToCSV()">📊 تصدير CSV</button>
                <button class="btn btn-success" onclick="exportToJSON()">💾 تصدير نسخة احتياطية</button>
                <label for="importFile" class="file-input-label">📁 استيراد نسخة احتياطية</label>
                <input type="file" id="importFile" class="file-input" accept=".json" onchange="importData()">
                <button class="btn btn-warning" onclick="resetData()">🔄 إعادة تعيين</button>
                <button class="btn btn-danger" onclick="clearLocalStorage()">🗂️ مسح التخزين المحلي</button>
                <button class="btn btn-danger" onclick="clearAllData()">🗑️ مسح الكل</button>
            </div>
            <div style="margin-top: 15px; padding: 10px; background: #f8f9fa; border-radius: 8px; font-size: 14px; color: #666;">
                💡 <strong>ملاحظة:</strong> يتم حفظ البيانات تلقائياً في التخزين المحلي للمتصفح. البيانات ستبقى محفوظة حتى بعد إغلاق المتصفح.
            </div>
        </div>
    </div>

    <!-- Leave Management Modal -->
    <div id="leaveModal" class="modal">
        <div class="modal-content">
            <span class="close" onclick="closeLeaveModal()">&times;</span>
            <h2>إدارة إجازات: <span id="modalEmployeeName"></span></h2>
            
            <div class="form-row">
                <div class="form-group">
                    <label>نوع الإجازة</label>
                    <select id="leaveType">
                        <option value="annual">إجازة سنوية</option>
                        <option value="sick">إجازة مرضية</option>
                        <option value="emergency">إجازة طارئة</option>
                    </select>
                </div>
                <div class="form-group">
                    <label>عدد الأيام</label>
                    <input type="number" id="leaveDays" min="0.5" max="365" step="0.5"
                           placeholder="أدخل عدد الأيام"
                           oninput="validateNumberInput(this, 0.5, 365)"
                           title="أدخل رقم بين 0.5 و 365">
                </div>
            </div>
            
            <div class="form-row">
                <div class="form-group">
                    <label>تاريخ البداية</label>
                    <input type="date" id="leaveStartDate"
                           onchange="validateDateInput(this, true); calculateLeaveDays()"
                           title="أدخل تاريخ بداية الإجازة">
                </div>
                <div class="form-group">
                    <label>تاريخ النهاية</label>
                    <input type="date" id="leaveEndDate"
                           onchange="validateDateInput(this, true); calculateLeaveDays()"
                           title="أدخل تاريخ نهاية الإجازة">
                </div>
            </div>
            
            <div class="form-group">
                <label>السبب / الملاحظات</label>
                <input type="text" id="leaveReason" placeholder="أدخل سبب الإجازة (اختياري)">
            </div>
            
            <div style="text-align: center; margin: 20px 0;">
                <button class="btn btn-success" onclick="addLeaveRecord()">إضافة الإجازة</button>
                <button class="btn" onclick="closeLeaveModal()">إلغاء</button>
            </div>
            
            <div class="leave-history" id="leaveHistory">
                <h4 style="text-align: center; padding: 15px; margin: 0; background: #f8f9fa;">سجل الإجازات</h4>
                <div id="leaveHistoryContent"></div>
            </div>
        </div>
    </div>

    <script>
        let employees = [];
        let filteredEmployees = [];
        let editingIndex = -1;
        let currentEmployeeIndex = -1;

        // Enhanced auto-save functionality with localStorage
        function saveData() {
            try {
                const data = {
                    employees: employees,
                    lastSaved: new Date().toISOString(),
                    version: '2.1'
                };

                // Save to localStorage
                localStorage.setItem('employeeLeaveSystem', JSON.stringify(data));

                // Create a JSON string for export
                const jsonData = JSON.stringify(data, null, 2);

                showSaveIndicator('saving');

                setTimeout(() => {
                    showSaveIndicator('saved');
                }, 500);

                return jsonData;
            } catch (error) {
                console.error('Error saving data:', error);
                showAlert('خطأ في حفظ البيانات: ' + error.message, 'danger');
                return null;
            }
        }

        // Load data from localStorage
        function loadDataFromStorage() {
            try {
                const savedData = localStorage.getItem('employeeLeaveSystem');
                if (savedData) {
                    const data = JSON.parse(savedData);

                    // Validate data structure
                    if (data.employees && Array.isArray(data.employees)) {
                        employees = data.employees;

                        // Ensure all employees have required properties
                        employees = employees.map(emp => ({
                            name: emp.name || '',
                            id: emp.id || '',
                            department: emp.department || '',
                            hireDate: emp.hireDate || new Date().toISOString().split('T')[0],
                            annualLeave: emp.annualLeave || 30,
                            sickLeave: emp.sickLeave || 15,
                            emergencyLeave: emp.emergencyLeave || 5,
                            usedAnnual: emp.usedAnnual || 0,
                            usedSick: emp.usedSick || 0,
                            usedEmergency: emp.usedEmergency || 0,
                            leaveHistory: emp.leaveHistory || []
                        }));

                        console.log(`تم تحميل ${employees.length} موظف من التخزين المحلي`);
                        showAlert(`تم استرجاع بيانات ${employees.length} موظف من التخزين المحلي`, 'success');
                        return true;
                    }
                }
                return false;
            } catch (error) {
                console.error('Error loading data from localStorage:', error);
                showAlert('خطأ في تحميل البيانات المحفوظة: ' + error.message, 'warning');
                return false;
            }
        }

        // Clear localStorage
        function clearLocalStorage() {
            try {
                localStorage.removeItem('employeeLeaveSystem');
                showAlert('تم مسح البيانات المحفوظة محلياً', 'success');
            } catch (error) {
                console.error('Error clearing localStorage:', error);
                showAlert('خطأ في مسح البيانات المحفوظة', 'danger');
            }
        }

        // Check localStorage availability
        function isLocalStorageAvailable() {
            try {
                const test = 'test';
                localStorage.setItem(test, test);
                localStorage.removeItem(test);
                return true;
            } catch (error) {
                return false;
            }
        }

        // Security functions for data sanitization
        function sanitizeInput(input) {
            if (typeof input !== 'string') return input;

            // Remove HTML tags and dangerous characters
            return input
                .replace(/[<>]/g, '') // Remove < and >
                .replace(/javascript:/gi, '') // Remove javascript: protocol
                .replace(/on\w+=/gi, '') // Remove event handlers like onclick=
                .replace(/&/g, '&amp;') // Escape ampersand
                .replace(/"/g, '&quot;') // Escape quotes
                .replace(/'/g, '&#x27;') // Escape single quotes
                .trim(); // Remove leading/trailing whitespace
        }

        function sanitizeEmployee(employee) {
            return {
                name: sanitizeInput(employee.name),
                id: sanitizeInput(employee.id),
                department: sanitizeInput(employee.department),
                hireDate: employee.hireDate, // Date inputs are safe
                annualLeave: parseInt(employee.annualLeave) || 0,
                sickLeave: parseInt(employee.sickLeave) || 0,
                emergencyLeave: parseInt(employee.emergencyLeave) || 0,
                usedAnnual: parseFloat(employee.usedAnnual) || 0,
                usedSick: parseFloat(employee.usedSick) || 0,
                usedEmergency: parseFloat(employee.usedEmergency) || 0,
                leaveHistory: employee.leaveHistory ? employee.leaveHistory.map(record => ({
                    id: record.id,
                    type: sanitizeInput(record.type),
                    typeName: sanitizeInput(record.typeName),
                    days: parseFloat(record.days) || 0,
                    startDate: record.startDate,
                    endDate: record.endDate,
                    reason: sanitizeInput(record.reason),
                    addedDate: record.addedDate
                })) : []
            };
        }

        function escapeHtml(text) {
            const div = document.createElement('div');
            div.textContent = text;
            return div.innerHTML;
        }

        // Enhanced validation functions
        function validateNumberInput(input, min, max) {
            const value = parseFloat(input.value);

            // Remove any invalid characters
            input.value = input.value.replace(/[^0-9.-]/g, '');

            if (isNaN(value)) {
                input.style.borderColor = '#e74c3c';
                return false;
            }

            if (value < min || value > max) {
                input.style.borderColor = '#f39c12';
                input.title = `القيمة يجب أن تكون بين ${min} و ${max}`;
                return false;
            }

            input.style.borderColor = '#27ae60';
            input.title = '';
            return true;
        }

        function validateDateInput(dateInput, allowFuture = false) {
            const inputDate = new Date(dateInput.value);
            const today = new Date();
            today.setHours(0, 0, 0, 0);

            if (isNaN(inputDate.getTime())) {
                dateInput.style.borderColor = '#e74c3c';
                return false;
            }

            if (!allowFuture && inputDate > today) {
                dateInput.style.borderColor = '#f39c12';
                dateInput.title = 'التاريخ لا يمكن أن يكون في المستقبل';
                return false;
            }

            // Check if date is too far in the past (more than 50 years)
            const fiftyYearsAgo = new Date();
            fiftyYearsAgo.setFullYear(fiftyYearsAgo.getFullYear() - 50);

            if (inputDate < fiftyYearsAgo) {
                dateInput.style.borderColor = '#f39c12';
                dateInput.title = 'التاريخ قديم جداً';
                return false;
            }

            dateInput.style.borderColor = '#27ae60';
            dateInput.title = '';
            return true;
        }

        function validateTextInput(input, pattern, errorMessage) {
            const value = input.value.trim();

            if (!value) {
                input.style.borderColor = '#e74c3c';
                return false;
            }

            if (pattern && !pattern.test(value)) {
                input.style.borderColor = '#f39c12';
                input.title = errorMessage;
                return false;
            }

            input.style.borderColor = '#27ae60';
            input.title = '';
            return true;
        }

        // Search and Filter Functions
        function filterEmployees() {
            const searchTerm = document.getElementById('searchInput').value.toLowerCase().trim();
            const statusFilter = document.getElementById('statusFilter').value;
            const departmentFilter = document.getElementById('departmentFilter').value;

            filteredEmployees = employees.filter(employee => {
                // Search filter
                const matchesSearch = !searchTerm ||
                    employee.name.toLowerCase().includes(searchTerm) ||
                    employee.id.toLowerCase().includes(searchTerm) ||
                    employee.department.toLowerCase().includes(searchTerm);

                // Status filter
                const employeeStatus = getEmployeeStatus(employee);
                const matchesStatus = !statusFilter || employeeStatus === statusFilter;

                // Department filter
                const matchesDepartment = !departmentFilter || employee.department === departmentFilter;

                return matchesSearch && matchesStatus && matchesDepartment;
            });

            updateTable();
            updateFilterResults();
        }

        function updateFilterResults() {
            const totalEmployees = employees.length;
            const filteredCount = filteredEmployees.length;
            const resultsElement = document.getElementById('filterResults');

            if (filteredCount === totalEmployees) {
                resultsElement.textContent = `عرض جميع الموظفين (${totalEmployees})`;
            } else {
                resultsElement.textContent = `عرض ${filteredCount} من أصل ${totalEmployees} موظف`;
            }
        }

        function clearFilters() {
            document.getElementById('searchInput').value = '';
            document.getElementById('statusFilter').value = '';
            document.getElementById('departmentFilter').value = '';
            filteredEmployees = [...employees];
            updateTable();
            updateFilterResults();
        }

        function updateDepartmentFilter() {
            const departmentFilter = document.getElementById('departmentFilter');
            const currentValue = departmentFilter.value;

            // Get unique departments
            const departments = [...new Set(employees.map(emp => emp.department))].sort();

            // Clear existing options except the first one
            departmentFilter.innerHTML = '<option value="">جميع الأقسام</option>';

            // Add department options
            departments.forEach(dept => {
                const option = document.createElement('option');
                option.value = dept;
                option.textContent = dept;
                departmentFilter.appendChild(option);
            });

            // Restore previous selection if it still exists
            if (departments.includes(currentValue)) {
                departmentFilter.value = currentValue;
            }
        }

        function showSaveIndicator(status) {
            const indicator = document.getElementById('saveIndicator');
            indicator.className = 'save-indicator';
            
            if (status === 'saving') {
                indicator.className += ' saving';
                indicator.textContent = 'جاري الحفظ...';
            } else if (status === 'saved') {
                indicator.className += ' saved';
                indicator.textContent = 'تم الحفظ ✓';
                setTimeout(() => {
                    indicator.className = 'save-indicator';
                    indicator.textContent = 'محفوظ تلقائياً';
                }, 2000);
            }
        }

        // Enhanced load data function
        function loadData() {
            // Check if localStorage is available
            if (!isLocalStorageAvailable()) {
                showAlert('التخزين المحلي غير متاح في هذا المتصفح', 'warning');
                employees = [];
                updateTable();
                updateStats();
                return;
            }

            // Try to load from localStorage first
            const loaded = loadDataFromStorage();

            if (!loaded) {
                // Initialize with empty data if no saved data
                employees = [];
                showAlert('لا توجد بيانات محفوظة مسبقاً. ابدأ بإضافة الموظفين', 'info');
            }

            // Initialize filters
            filteredEmployees = [...employees];
            updateDepartmentFilter();
            updateTable();
            updateStats();
            updateFilterResults();
        }

        // Enhanced alert system
        function showAlert(message, type = 'success') {
            const alertContainer = document.getElementById('alertContainer');
            const alert = document.createElement('div');
            alert.className = `alert alert-${type}`;
            
            const icon = type === 'success' ? '✅' : type === 'warning' ? '⚠️' : type === 'danger' ? '❌' : 'ℹ️';
            alert.innerHTML = `${icon} ${message}`;
            
            alertContainer.appendChild(alert);
            
            setTimeout(() => {
                alert.remove();
            }, 4000);
        }

        // Enhanced employee addition with security
        function addEmployee() {
            const name = sanitizeInput(document.getElementById('employeeName').value.trim());
            const id = sanitizeInput(document.getElementById('employeeId').value.trim());
            const department = sanitizeInput(document.getElementById('department').value.trim());
            const hireDate = document.getElementById('hireDate').value;
            const annualLeave = parseInt(document.getElementById('annualLeave').value) || 0;
            const sickLeave = parseInt(document.getElementById('sickLeave').value) || 0;
            const emergencyLeave = parseInt(document.getElementById('emergencyLeave').value) || 0;

            // Enhanced validation
            if (!name || !id || !department || !hireDate) {
                showAlert('يرجى ملء جميع الحقول المطلوبة المميزة بـ *', 'warning');
                return;
            }

            // Validate name (only letters, spaces, and Arabic characters)
            if (!/^[\u0600-\u06FFa-zA-Z\s]+$/.test(name)) {
                showAlert('اسم الموظف يجب أن يحتوي على أحرف عربية أو إنجليزية فقط', 'warning');
                return;
            }

            // Validate employee ID (alphanumeric only)
            if (!/^[a-zA-Z0-9]+$/.test(id)) {
                showAlert('الرقم الوظيفي يجب أن يحتوي على أرقام وأحرف إنجليزية فقط', 'warning');
                return;
            }

            // Check if employee ID already exists (except when editing)
            if (editingIndex === -1 && employees.some(emp => emp.id === id)) {
                showAlert('الرقم الوظيفي موجود مسبقاً. يرجى استخدام رقم مختلف', 'warning');
                return;
            }

            // Check hire date is not in future
            if (new Date(hireDate) > new Date()) {
                showAlert('تاريخ التوظيف لا يمكن أن يكون في المستقبل', 'warning');
                return;
            }

            // Validate leave values (must be positive)
            if (annualLeave < 0 || sickLeave < 0 || emergencyLeave < 0) {
                showAlert('قيم الإجازات يجب أن تكون أرقام موجبة', 'warning');
                return;
            }

            // Validate reasonable leave limits
            if (annualLeave > 365 || sickLeave > 90 || emergencyLeave > 30) {
                showAlert('قيم الإجازات تبدو غير منطقية. يرجى المراجعة', 'warning');
                return;
            }

            const employee = sanitizeEmployee({
                name,
                id,
                department,
                hireDate,
                annualLeave,
                sickLeave,
                emergencyLeave,
                usedAnnual: 0,
                usedSick: 0,
                usedEmergency: 0,
                leaveHistory: []
            });

            if (editingIndex === -1) {
                employees.push(employee);
                showAlert(`تم إضافة الموظف ${escapeHtml(name)} بنجاح`, 'success');
            } else {
                // Preserve leave history when editing
                employee.leaveHistory = employees[editingIndex].leaveHistory || [];
                employees[editingIndex] = employee;
                showAlert(`تم تحديث بيانات الموظف ${escapeHtml(name)} بنجاح`, 'success');
                editingIndex = -1;
                document.getElementById('addEmployeeBtn').textContent = 'إضافة موظف';
            }

            clearForm();
            updateDepartmentFilter();
            filterEmployees();
            updateStats();
            saveData();
        }

        function clearForm() {
            document.getElementById('employeeName').value = '';
            document.getElementById('employeeId').value = '';
            document.getElementById('department').value = '';
            document.getElementById('hireDate').value = '';
            document.getElementById('annualLeave').value = '30';
            document.getElementById('sickLeave').value = '15';
            document.getElementById('emergencyLeave').value = '5';
        }

        function calculateYearsOfService(hireDate) {
            const today = new Date();
            const hire = new Date(hireDate);
            const years = (today - hire) / (365.25 * 24 * 60 * 60 * 1000);
            return Math.max(0, Math.floor(years));
        }

        function getEmployeeStatus(employee) {
            const remainingAnnual = employee.annualLeave - employee.usedAnnual;
            if (remainingAnnual <= 0) return 'نفد الرصيد';
            if (remainingAnnual <= 5) return 'رصيد منخفض';
            return 'طبيعي';
        }

        function updateTable() {
            const tbody = document.getElementById('employeeTableBody');
            tbody.innerHTML = '';

            // Use filtered employees if filters are active, otherwise use all employees
            const employeesToShow = filteredEmployees.length > 0 || hasActiveFilters() ? filteredEmployees : employees;

            employeesToShow.forEach((employee, filteredIndex) => {
                // Find the original index in the employees array
                const originalIndex = employees.findIndex(emp => emp.id === employee.id);
                const row = tbody.insertRow();
                const yearsOfService = calculateYearsOfService(employee.hireDate);
                const remainingAnnual = employee.annualLeave - employee.usedAnnual;
                const remainingSick = employee.sickLeave - employee.usedSick;
                const remainingEmergency = employee.emergencyLeave - employee.usedEmergency;
                const status = getEmployeeStatus(employee);

                let statusClass = 'balance-positive';
                if (remainingAnnual <= 0) statusClass = 'balance-negative';
                else if (remainingAnnual <= 5) statusClass = 'balance-warning';

                // Create cells safely using textContent for user data
                const cells = [
                    { content: employee.name, isText: true },
                    { content: employee.id, isText: true },
                    { content: employee.department, isText: true },
                    { content: new Date(employee.hireDate).toLocaleDateString('ar-SA'), isText: true },
                    { content: yearsOfService, isText: true },
                    { content: employee.annualLeave, isText: true },
                    { content: employee.usedAnnual, isText: true },
                    { content: remainingAnnual, isText: true, className: `leave-balance ${statusClass}` },
                    { content: remainingSick, isText: true, className: `leave-balance ${remainingSick <= 0 ? 'balance-negative' : remainingSick <= 2 ? 'balance-warning' : 'balance-positive'}` },
                    { content: remainingEmergency, isText: true, className: `leave-balance ${remainingEmergency <= 0 ? 'balance-negative' : 'balance-positive'}` },
                    { content: status, isText: true, className: statusClass }
                ];

                // Add data cells
                cells.forEach(cellData => {
                    const cell = row.insertCell();
                    if (cellData.isText) {
                        cell.textContent = cellData.content;
                    } else {
                        cell.innerHTML = cellData.content;
                    }
                    if (cellData.className) {
                        cell.className = cellData.className;
                    }
                });

                // Add action buttons cell (safe HTML) - use original index
                const actionsCell = row.insertCell();
                actionsCell.innerHTML = `
                    <button class="btn btn-small" onclick="editEmployee(${originalIndex})" title="تعديل البيانات">✏️ تعديل</button>
                    <button class="btn btn-success btn-small" onclick="openLeaveModal(${originalIndex})" title="إدارة الإجازات">📅 إجازات</button>
                    <button class="btn btn-danger btn-small" onclick="deleteEmployee(${originalIndex})" title="حذف الموظف">🗑️ حذف</button>
                `;
            });
        }

        function hasActiveFilters() {
            const searchTerm = document.getElementById('searchInput').value.trim();
            const statusFilter = document.getElementById('statusFilter').value;
            const departmentFilter = document.getElementById('departmentFilter').value;

            return searchTerm !== '' || statusFilter !== '' || departmentFilter !== '';
        }

        function editEmployee(index) {
            const employee = employees[index];
            document.getElementById('employeeName').value = employee.name;
            document.getElementById('employeeId').value = employee.id;
            document.getElementById('department').value = employee.department;
            document.getElementById('hireDate').value = employee.hireDate;
            document.getElementById('annualLeave').value = employee.annualLeave;
            document.getElementById('sickLeave').value = employee.sickLeave;
            document.getElementById('emergencyLeave').value = employee.emergencyLeave;
            
            editingIndex = index;
            document.getElementById('addEmployeeBtn').textContent = 'تحديث الموظف';
            
            // Scroll to top
            window.scrollTo(0, 0);
        }

        function deleteEmployee(index) {
            const employee = employees[index];
            if (confirm(`هل أنت متأكد من حذف الموظف "${employee.name}"؟\nسيتم حذف جميع سجلات الإجازات المرتبطة به.`)) {
                employees.splice(index, 1);
                updateDepartmentFilter();
                filterEmployees();
                updateStats();
                saveData();
                showAlert(`تم حذف الموظف "${escapeHtml(employee.name)}" بنجاح`, 'success');
            }
        }

        // Enhanced leave management
        function openLeaveModal(index) {
            currentEmployeeIndex = index;
            const employee = employees[index];
            
            document.getElementById('modalEmployeeName').textContent = employee.name;
            document.getElementById('leaveType').value = 'annual';
            document.getElementById('leaveDays').value = '';
            document.getElementById('leaveStartDate').value = '';
            document.getElementById('leaveEndDate').value = '';
            document.getElementById('leaveReason').value = '';
            
            updateLeaveHistory();
            document.getElementById('leaveModal').style.display = 'block';
        }

        function closeLeaveModal() {
            document.getElementById('leaveModal').style.display = 'none';
            currentEmployeeIndex = -1;
        }

        function addLeaveRecord() {
            if (currentEmployeeIndex === -1) return;

            const employee = employees[currentEmployeeIndex];
            const leaveType = sanitizeInput(document.getElementById('leaveType').value);
            const leaveDays = parseFloat(document.getElementById('leaveDays').value);
            const startDate = document.getElementById('leaveStartDate').value;
            const endDate = document.getElementById('leaveEndDate').value;
            const reason = sanitizeInput(document.getElementById('leaveReason').value.trim());

            // Validation
            if (!leaveDays || leaveDays <= 0) {
                showAlert('يرجى إدخال عدد أيام صحيح', 'warning');
                return;
            }

            if (!startDate || !endDate) {
                showAlert('يرجى تحديد تاريخ البداية والنهاية', 'warning');
                return;
            }

            if (new Date(startDate) > new Date(endDate)) {
                showAlert('تاريخ البداية يجب أن يكون قبل تاريخ النهاية', 'warning');
                return;
            }

            // Check available balance
            let currentUsed = 0;
            let maxAllowed = 0;
            let leaveTypeName = '';

            switch(leaveType) {
                case 'annual':
                    currentUsed = employee.usedAnnual;
                    maxAllowed = employee.annualLeave;
                    leaveTypeName = 'إجازة سنوية';
                    break;
                case 'sick':
                    currentUsed = employee.usedSick;
                    maxAllowed = employee.sickLeave;
                    leaveTypeName = 'إجازة مرضية';
                    break;
                case 'emergency':
                    currentUsed = employee.usedEmergency;
                    maxAllowed = employee.emergencyLeave;
                    leaveTypeName = 'إجازة طارئة';
                    break;
            }

            if (currentUsed + leaveDays > maxAllowed) {
                const remaining = maxAllowed - currentUsed;
                showAlert(`الرصيد غير كافي. المتبقي: ${remaining} يوم فقط`, 'warning');
                return;
            }

            // Add leave record
            const leaveRecord = {
                id: Date.now(),
                type: leaveType,
                typeName: leaveTypeName,
                days: leaveDays,
                startDate,
                endDate,
                reason: reason || 'غير محدد',
                addedDate: new Date().toISOString()
            };

            if (!employee.leaveHistory) {
                employee.leaveHistory = [];
            }
            employee.leaveHistory.push(leaveRecord);

            // Update used days
            switch(leaveType) {
                case 'annual':
                    employee.usedAnnual += leaveDays;
                    break;
                case 'sick':
                    employee.usedSick += leaveDays;
                    break;
                case 'emergency':
                    employee.usedEmergency += leaveDays;
                    break;
            }

            filterEmployees();
            updateStats();
            updateLeaveHistory();
            saveData();

            showAlert(`تم إضافة ${leaveTypeName} لمدة ${leaveDays} يوم للموظف ${employee.name}`, 'success');

            // Clear form
            document.getElementById('leaveDays').value = '';
            document.getElementById('leaveStartDate').value = '';
            document.getElementById('leaveEndDate').value = '';
            document.getElementById('leaveReason').value = '';
        }

        function updateLeaveHistory() {
            if (currentEmployeeIndex === -1) return;

            const employee = employees[currentEmployeeIndex];
            const historyContent = document.getElementById('leaveHistoryContent');

            if (!employee.leaveHistory || employee.leaveHistory.length === 0) {
                historyContent.innerHTML = '<p style="text-align: center; padding: 20px; color: #666;">لا توجد إجازات مسجلة</p>';
                return;
            }

            // Sort by date (newest first)
            const sortedHistory = [...employee.leaveHistory].sort((a, b) => new Date(b.addedDate) - new Date(a.addedDate));

            historyContent.innerHTML = sortedHistory.map(record => `
                <div class="leave-record">
                    <div class="leave-record-header">
                        <strong>${record.typeName}</strong>
                        <button class="delete-leave" onclick="deleteLeaveRecord(${record.id})" title="حذف الإجازة">×</button>
                    </div>
                    <div>📅 من ${new Date(record.startDate).toLocaleDateString('ar-SA')} إلى ${new Date(record.endDate).toLocaleDateString('ar-SA')}</div>
                    <div>⏱️ المدة: ${record.days} يوم</div>
                    <div>📝 السبب: ${record.reason}</div>
                    <div style="font-size: 12px; color: #666; margin-top: 5px;">تم الإضافة: ${new Date(record.addedDate).toLocaleDateString('ar-SA')}</div>
                </div>
            `).join('');
        }

        function deleteLeaveRecord(recordId) {
            if (currentEmployeeIndex === -1) return;

            const employee = employees[currentEmployeeIndex];
            const recordIndex = employee.leaveHistory.findIndex(record => record.id === recordId);

            if (recordIndex === -1) return;

            const record = employee.leaveHistory[recordIndex];

            if (confirm(`هل أنت متأكد من حذف إجازة ${record.typeName} لمدة ${record.days} يوم؟`)) {
                // Restore the used days
                switch(record.type) {
                    case 'annual':
                        employee.usedAnnual -= record.days;
                        break;
                    case 'sick':
                        employee.usedSick -= record.days;
                        break;
                    case 'emergency':
                        employee.usedEmergency -= record.days;
                        break;
                }

                // Remove the record
                employee.leaveHistory.splice(recordIndex, 1);

                filterEmployees();
                updateStats();
                updateLeaveHistory();
                saveData();

                showAlert(`تم حذف إجازة ${record.typeName} بنجاح`, 'success');
            }
        }

        function updateStats() {
            const totalEmployees = employees.length;
            const totalUsedLeaves = employees.reduce((sum, emp) => sum + emp.usedAnnual + emp.usedSick + emp.usedEmergency, 0);
            const totalRemainingLeaves = employees.reduce((sum, emp) =>
                sum + (emp.annualLeave - emp.usedAnnual) + (emp.sickLeave - emp.usedSick) + (emp.emergencyLeave - emp.usedEmergency), 0);
            const totalAllowedLeaves = employees.reduce((sum, emp) => sum + emp.annualLeave + emp.sickLeave + emp.emergencyLeave, 0);
            const averageUsage = totalAllowedLeaves > 0 ? Math.round((totalUsedLeaves / totalAllowedLeaves) * 100) : 0;

            document.getElementById('totalEmployees').textContent = totalEmployees;
            document.getElementById('totalUsedLeaves').textContent = totalUsedLeaves;
            document.getElementById('totalRemainingLeaves').textContent = totalRemainingLeaves;
            document.getElementById('averageUsage').textContent = averageUsage + '%';
        }

        // Export functions
        function exportToCSV() {
            if (employees.length === 0) {
                showAlert('لا توجد بيانات للتصدير', 'warning');
                return;
            }

            const headers = [
                'اسم الموظف', 'الرقم الوظيفي', 'القسم', 'تاريخ التوظيف', 'سنوات الخدمة',
                'الرصيد السنوي', 'المستخدم السنوي', 'المتبقي السنوي',
                'الرصيد المرضي', 'المستخدم المرضي', 'المتبقي المرضي',
                'الرصيد الطارئ', 'المستخدم الطارئ', 'المتبقي الطارئ', 'الحالة'
            ];

            const csvContent = [
                headers.join(','),
                ...employees.map(emp => {
                    const yearsOfService = calculateYearsOfService(emp.hireDate);
                    const status = getEmployeeStatus(emp);
                    return [
                        emp.name, emp.id, emp.department, emp.hireDate, yearsOfService,
                        emp.annualLeave, emp.usedAnnual, emp.annualLeave - emp.usedAnnual,
                        emp.sickLeave, emp.usedSick, emp.sickLeave - emp.usedSick,
                        emp.emergencyLeave, emp.usedEmergency, emp.emergencyLeave - emp.usedEmergency,
                        status
                    ].join(',');
                })
            ].join('\n');

            downloadFile(csvContent, 'employee_leaves.csv', 'text/csv;charset=utf-8;');
            showAlert('تم تصدير البيانات بصيغة CSV بنجاح', 'success');
        }

        function exportToJSON() {
            const data = saveData();
            downloadFile(data, `employee_leaves_backup_${new Date().toISOString().split('T')[0]}.json`, 'application/json');
            showAlert('تم إنشاء النسخة الاحتياطية بنجاح', 'success');
        }

        function downloadFile(content, filename, contentType) {
            const blob = new Blob([content], { type: contentType });
            const url = window.URL.createObjectURL(blob);
            const link = document.createElement('a');
            link.href = url;
            link.download = filename;
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);
            window.URL.revokeObjectURL(url);
        }

        function importData() {
            const fileInput = document.getElementById('importFile');
            const file = fileInput.files[0];

            if (!file) return;

            const reader = new FileReader();
            reader.onload = function(e) {
                try {
                    const data = JSON.parse(e.target.result);

                    if (data.employees && Array.isArray(data.employees)) {
                        if (confirm('هل أنت متأكد من استيراد البيانات؟ سيتم استبدال البيانات الحالية.')) {
                            employees = data.employees;
                            updateTable();
                            updateStats();
                            saveData();
                            showAlert(`تم استيراد ${employees.length} موظف بنجاح`, 'success');
                        }
                    } else {
                        showAlert('ملف غير صالح. يرجى التأكد من صحة الملف', 'danger');
                    }
                } catch (error) {
                    showAlert('خطأ في قراءة الملف. يرجى التأكد من صحة الملف', 'danger');
                }
            };
            reader.readAsText(file);

            // Clear the file input
            fileInput.value = '';
        }

        function resetData() {
            if (confirm('هل أنت متأكد من إعادة تعيين جميع الإجازات المستخدمة؟\nسيتم الاحتفاظ ببيانات الموظفين ولكن سيتم مسح سجل الإجازات.')) {
                employees.forEach(emp => {
                    emp.usedAnnual = 0;
                    emp.usedSick = 0;
                    emp.usedEmergency = 0;
                    emp.leaveHistory = [];
                });
                updateTable();
                updateStats();
                saveData();
                showAlert('تم إعادة تعيين البيانات بنجاح', 'success');
            }
        }

        function clearAllData() {
            if (confirm('هل أنت متأكد من حذف جميع البيانات؟\nسيتم حذف البيانات من التخزين المحلي أيضاً.\nهذا الإجراء لا يمكن التراجع عنه.')) {
                if (confirm('تأكيد أخير: سيتم حذف جميع بيانات الموظفين والإجازات نهائياً من الذاكرة والتخزين المحلي!')) {
                    employees = [];
                    clearLocalStorage();
                    updateTable();
                    updateStats();
                    showAlert('تم مسح جميع البيانات من الذاكرة والتخزين المحلي', 'success');
                }
            }
        }

        // Date calculation helpers
        function calculateLeaveDays() {
            const startDate = document.getElementById('leaveStartDate').value;
            const endDate = document.getElementById('leaveEndDate').value;

            if (startDate && endDate) {
                const start = new Date(startDate);
                const end = new Date(endDate);
                const diffTime = Math.abs(end - start);
                const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24)) + 1; // +1 to include both start and end dates

                document.getElementById('leaveDays').value = diffDays;
            }
        }

        // Event listeners
        document.getElementById('leaveStartDate').addEventListener('change', calculateLeaveDays);
        document.getElementById('leaveEndDate').addEventListener('change', calculateLeaveDays);

        // Close modal when clicking outside
        window.onclick = function(event) {
            const modal = document.getElementById('leaveModal');
            if (event.target === modal) {
                closeLeaveModal();
            }
        }

        // Keyboard shortcuts
        document.addEventListener('keydown', function(e) {
            if (e.key === 'Escape') {
                closeLeaveModal();
            }
        });

        // Initialize the application
        document.addEventListener('DOMContentLoaded', function() {
            loadData();

            // Set default hire date to today
            document.getElementById('hireDate').value = new Date().toISOString().split('T')[0];

            // Show welcome message based on localStorage availability
            if (isLocalStorageAvailable()) {
                showAlert('مرحباً بك في نظام إدارة إجازات الموظفين المتطور مع الحفظ التلقائي', 'success');
            } else {
                showAlert('مرحباً بك في نظام إدارة إجازات الموظفين - تحذير: التخزين المحلي غير متاح', 'warning');
            }
        });

        // Auto-save every 30 seconds
        setInterval(() => {
            if (employees.length > 0) {
                saveData();
            }
        }, 30000);
    </script>
</body>
</html>